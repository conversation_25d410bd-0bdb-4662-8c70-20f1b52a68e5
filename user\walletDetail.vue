<template>
	<view class="page">
		<view class="status-card">
		<!-- 	<view class="status-icon" :class="statusIconClass">
				<text class="iconfont" :class="statusIcon"></text>
			</view> -->
			<view class="status-text">{{ detail.status }}</view>
			<view class="amount">￥{{ detail.truePrice || 0 }}</view>
			<!-- <view class="fail-reason" v-if="detail.friendlyFailReason">
				{{ detail.friendlyFailReason }}
			</view> -->
		</view>

		<view class="detail-section">
			<view class="section-title">提现详情</view>
			<view class="detail-list">
				<view class="detail-item">
					<text class="label">订单编号</text>
					<text class="value">{{ detail.code }}</text>
				</view>
				<view class="detail-item">
					<text class="label">审核状态</text>
					<text class="value">{{ detail.lock }}</text>
				</view>
				<view v-if="detail.lock==='审核拒绝'" class="detail-item">
					<text class="label">拒绝原因</text>
					<text class="value">{{ detail.text?detail.text:'' }}</text>
				</view>
				<view class="detail-item">
					<text class="label">申请金额</text>
					<text class="value">￥{{ detail.applyPrice || 0 }}</text>
				</view>
				<view class="detail-item">
					<text class="label">服务费</text>
					<text class="value">￥{{ detail.servicePrice || 0 }}</text>
				</view>
				<view class="detail-item">
					<text class="label">实际到账</text>
					<text class="value highlight">￥{{ detail.truePrice || 0 }}</text>
				</view>
				<view class="detail-item">
					<text class="label">提现类型</text>
					<text class="value">{{ detail.type }}</text>
				</view>
				<view class="detail-item">
					<text class="label">提现方式</text>
					<text class="value">{{ detail.cashToType }}</text>
				</view>
				<view class="detail-item">
					<text class="label">申请时间</text>
					<text class="value">{{ detail.createTime }}</text>
				</view>
				<view class="detail-item" v-if="detail.shTime">
					<text class="label">到账时间</text>
					<text class="value">{{ detail.shTime }}</text>
				</view>

			</view>
		</view>

		<view class="action-section" v-if="showActions">
			<button class="action-btn primary" @tap="retryWithdraw" v-if="canRetry">
				重新提现
			</button>
			<button class="action-btn" @tap="contactService">
				联系客服
			</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id: '', // 提现记录ID
				detail: {}, // 详情数据
				loading: false
			}
		},
		computed: {
			// 是否显示操作按钮
			showActions() {
				return this.detail.status === '失败' || this.detail.status === '关闭';
			},
			// 是否可以重新提现
			canRetry() {
				return this.detail.status === '失败';
			},
			// 状态图标类
			statusIconClass() {
				const classMap = {
					'失败': 'status-failed',
					'关闭': 'status-closed',
					'已到账': 'status-success',
					'已提现未领取': 'status-processing',
					'未提现': 'status-pending'
				};
				return classMap[this.detail.status] || 'status-default';
			},
			// 状态图标
			statusIcon() {
				const iconMap = {
					'失败': 'icon-close',
					'关闭': 'icon-close',
					'已到账': 'icon-right',
					'已提现未领取': 'icon-time',
					'未提现': 'icon-time'
				};
				return iconMap[this.detail.status] || 'icon-time';
			}
		},
		onLoad(options) {
			if (options.id) {
				this.id = options.id;
				this.getDetail();
			}
		},
		methods: {
			// 获取详情
			async getDetail() {
				if (this.loading) return;
				
				this.loading = true;
				uni.showLoading({ title: '加载中...' });
				
				try {
					const res = await this.$api.mine.walletDetail(this.id);
					
					if (res.code === '200') {
						this.detail = res.data || {};
					} else {
						uni.showToast({
							title: res.msg || '获取详情失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('获取详情失败:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none'
					});
				} finally {
					this.loading = false;
					uni.hideLoading();
				}
			},
			

			
			// 重新提现
			retryWithdraw() {
				uni.navigateTo({
					url: '/user/cashOut'
				});
			},
			
			// 联系客服
			contactService() {
				uni.showModal({
					title: '联系客服',
					content: '客服电话：4008326986',
					confirmText: '拨打',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							uni.makePhoneCall({
								phoneNumber: '4008326986'
							});
						}
					}
				});
			}
		}
	}
</script>

<style scoped lang="scss">
/* 定义更现代、柔和且专业的颜色变量 */
$success-color: #4CAF50; // 成功：更鲜活的绿
$fail-color: #F44336;    // 失败：更醒目的红
$closed-color: #9E9E9E;  // 关闭：中性灰
$processing-color: #2196F3; // 处理中：标准蓝
$pending-color: #FFC107; // 待处理：明亮黄

$text-primary: #333333; // 主要文本色：深灰
$text-secondary: #757575; // 次要文本色：中灰
$background-light: #f8f8f8; // 页面背景：更浅的灰
$card-background: #ffffff; // 卡片背景：白色
$border-color: #eeeeee; // 边框色：浅灰
$highlight-accent: #1976D2; // 强调色：深蓝色，用于突出显示

.page {
	background-color: $background-light;
	min-height: 100vh;
	padding-bottom: 40rpx; /* 增加页面底部内边距，防止内容紧贴底部 */
}

.status-card {
	background: $card-background;
	padding: 60rpx 30rpx;
	text-align: center;
	margin-bottom: 20rpx;
	border-radius: 16rpx; /* 增加卡片圆角 */
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05); /* 增加细微阴影，提升层次感 */
	
	.status-icon {
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 auto 30rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1); /* 图标阴影 */
		
		.iconfont {
			font-size: 60rpx;
			color: #ffffff;
		}
		
		&.status-success {
			background: $success-color;
		}
		
		&.status-failed {
			background: $fail-color;
		}
		
		&.status-closed {
			background: $closed-color;
		}
		
		&.status-processing {
			background: $processing-color;
		}
		
		&.status-pending {
			background: $pending-color;
		}
	}
	
	.status-text {
		font-size: 36rpx; /* 状态文本字号加大 */
		font-weight: bold; /* 加粗 */
		color: $text-primary;
		margin-bottom: 16rpx; /* 调整间距 */
	}
	
	.amount {
		font-size: 56rpx; /* 金额字号加大 */
		font-weight: bold;
		color: $text-primary;
		margin-bottom: 24rpx; /* 调整间距 */
	}
	
	.fail-reason {
		font-size: 28rpx; /* 失败原因字号 */
		color: $fail-color;
		background: lighten($fail-color, 40%); /* 更浅的背景 */
		padding: 12rpx 24rpx;
		border-radius: 30rpx; /* 更多圆角 */
		display: inline-block;
		font-weight: 500;
	}
}

.detail-section {
	background: $card-background;
	margin: 0 20rpx 20rpx; /* 左右增加间距，与卡片保持一致 */
	border-radius: 16rpx; /* 增加卡片圆角 */
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05); /* 增加细微阴影 */

	.section-title {
		padding: 30rpx;
		font-size: 32rpx;
		font-weight: bold; /* 标题加粗 */
		color: $text-primary;
		border-bottom: 1rpx solid $border-color;
	}
	
	.detail-list {
		.detail-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 24rpx 30rpx; /* 调整垂直内边距 */
			border-bottom: 1rpx solid $border-color;
			
			&:last-child {
				border-bottom: none;
			}
			
			.label {
				font-size: 28rpx;
				color: $text-secondary;
			}
			
			.value {
				font-size: 28rpx;
				color: $text-primary;
				text-align: right;
				max-width: 400rpx;
				word-break: break-all;
				line-height: 1.5; /* 增加行高，提升可读性 */
				
				&.highlight {
					color: $highlight-accent; /* 强调色 */
					font-weight: bold; /* 加粗 */
				}
			}
		}
	}
}

.action-section {
	padding: 40rpx 30rpx;
	display: flex;
	gap: 24rpx; /* 增加按钮间距 */
	
	.action-btn {
		flex: 1;
		height: 96rpx; /* 按钮高度增加 */
		border-radius: 48rpx; /* 圆角更大 */
		font-size: 34rpx; /* 按钮字体加大 */
		font-weight: 500;
		border: 1rpx solid $border-color;
		background: $card-background;
		color: $text-primary;
		display: flex; /* 确保内容居中 */
		align-items: center;
		justify-content: center;
		
		&.primary {
			background: $highlight-accent;
			color: #ffffff;
			border-color: $highlight-accent;
			box-shadow: 0 6rpx 12rpx rgba($highlight-accent, 0.3); /* 主按钮增加阴影 */
		}
		
		&:active {
			opacity: 0.8; /* 按钮点击态 */
		}
	}
}
</style>