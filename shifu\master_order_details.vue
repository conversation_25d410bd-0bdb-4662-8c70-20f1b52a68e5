<template>
	<view class="page" v-if="ready">
		<view class="box">
			<view class="title">
				<image src="../static/images/8957.png" mode="aspectFill" class="title-icon"></image>
				<span>订单信息</span>
			</view>

			<view class="info-box">
				<view class="info-item" v-for="(info, index) in orderDetails" :key="index">
					<text class="label">{{ info.label }}</text>
					<text :class="['value', info.alignRight ? 'align-right' : '']">{{ info.value }}</text>
				</view>
				<view class="navigation-btn" @click="godh">
					<image src="../static/images/9349.png" mode="aspectFill" class="nav-icon"></image>
					<text>导航</text>
				</view>
			</view>

			<view class="dynamic-section" v-for="(item, index) in Info.settingOrderList" :key="index">
				<view class="title">{{ item.problemDesc }}</view>
				<view class="img-box" v-if="isValidImageUrl(item.val)">
					<image v-for="(url, imgIndex) in splitImageUrls(item.val)" :key="imgIndex" :src="url.trim()"
						mode="aspectFill" @error="onImageError(url, index, imgIndex)" @click="previewImage(url)"
						class="dynamic-image"></image>
				</view>
				<view v-else-if="item.val && item.val.trim() !== ''" class="text-box">
					<text>{{ item.val }}</text>
				</view>
				<view v-else class="text-box">
					<text>无</text>
				</view>
			</view>

			<view class="">
				<rich-text :nodes="getconfigs.masterNotice"></rich-text>
			</view>
			<view class="image-modal" v-if="showImageModal" @click="closeImageModal">
				<view class="modal-content" @click.stop>
					<image :src="currentImage" mode="aspectFit" class="modal-image"></image>
					<view class="close-btn" @click="closeImageModal">关闭</view>
				</view>
			</view>
		</view>

		<view class="bottom-quote-section">
			<view class="quote-btn" @click="handleQuote">
				立即报价
			</view>
		</view>

		<u-popup :show="show" :round="10" closeable @close="close">
			<view class="quote-popup-box">
				<view class="popup-title">立即报价</view>
				<view class="popup-subtitle">报价金额</view>
				<view class="money-input">
					<u--input placeholder="请输入报价金额" prefixIcon="rmb" prefixIconStyle="font-size: 22px;color: #909399"
						type="digit" v-model="input" @input="validateInput" maxlength="5"></u--input>
				</view>
				<view class="">
					<rich-text :nodes="getconfigs.masterNotice"></rich-text>
				</view>
				<view class="confirm-quote-btn" @click="confirmBao">确认报价</view>
			</view>
		</u-popup>

		<u-modal :show="confirmshow" :content="content" showCancelButton @confirm="confirmRe"
			@cancel="confirmshow = false"></u-modal>
		<u-modal :show="masterModalShow" content="成为师傅才能操作" showCancelButton @confirm="goToSettle"
			@cancel="masterModalShow = false"></u-modal>

		<u-modal :show="showNameIdModal" title="请完善实名信息以确保提现安全" confirmText="保存" showCancelButton @confirm="saveNameIdInfo"
			@cancel="showNameIdModal = false" :contentStyle="{ padding: '40rpx', background: '#ffffff', borderRadius: '16rpx' }">
			<view class="slot-content">
				<view class="main_item">
					<view class="title"><span style="color: #E41F19;">*</span>姓名</view>
					<input type="text" v-model="tempForm.coachName" placeholder="请输入姓名" class="modal-input" />
				</view>
				<view class="main_item">
					<view class="title"><span style="color: #E41F19;">*</span>身份证号</view>
					<input type="text" v-model="tempForm.idCode" placeholder="请输入身份证号" class="modal-input" />
				</view>
				<view class="main_item">
					<view class="title"><span style="color: #E41F19;">*</span>上传身份证照片</view>
					<view class="card">
						<view class="card_item">
							<view class="top">
								<view class="das">
									<view class="up">
										<upload
											@upload="imgUploadTemp"
											@del="imgUploadTemp"
											:imagelist="tempForm.id_card1"
											imgtype="id_card1"
											imgclass="id_card_box"
											text="身份证人像面"
											:imgsize="1"
										></upload>
									</view>
								</view>
							</view>
							<view class="bottom">拍摄人像面</view>
						</view>
						<view class="card_item">
							<view class="top">
								<view class="das">
									<view class="up">
										<upload
											@upload="imgUploadTemp"
											@del="imgUploadTemp"
											:imagelist="tempForm.id_card2"
											imgtype="id_card2"
											imgclass="id_card_box"
											text="身份证国徽面"
											:imgsize="1"
										></upload>
									</view>
								</view>
							</view>
							<view class="bottom">拍摄国徽面</view>
						</view>
					</view>
				</view>
			</view>
		</u-modal>
	</view>
</template>

<script>
	import Upload from '@/components/upload.vue'; // Adjust path to your upload.vue
	export default {
		components: {
			Upload,
		},
		data() {
			return {
				ready: false,
				id: '',
				configInfo: '',
				selectedItem: {},
				payType: {},
				Info: {},
				imageErrors: [], // Track images that failed to load
				fallbackImage: '../static/images/placeholder.png', // Placeholder image path
				showImageModal: false, // Control image modal visibility
				currentImage: '', // Store the currently displayed image URL
				// 报价相关数据
				show: false,
				getconfigs:'',
				confirmshow: false,
				masterModalShow: false,
				showNameIdModal: false, // New state for the name/ID modal
				content: '确认接下该订单吗',
				input: '',
				orderData: '',
				tmplIds: ['qt5Q70oqYNTB2RaTzl0peEHpjK-iinWr3ZfxGvs4wtg', 'DxiqXzK4yxCTYAqmeK9lEs0A5-XCF9Fy7kSyX2vmnk',
					'_2z7Bbw8oq4av-yqP31fZLaI82Z_N52wNM_1ihXQv6I'
				],
				tempForm: { // Temporary form for name/ID input
					coachName: '',
					idCode: '',
					id_card1: [],
					id_card2: [],
				}
			};
		},
		computed: {
			orderDetails() {
				return [{
						label: '订单单号',
						value: this.formatOrderCode(this.selectedItem.orderCode),
						alignRight: true
					},
					{
						label: '服务内容',
						value: this.selectedItem.goodsName || ''
					},
					{
						label: '下单时间',
						value: this.$util.timestampToTime(this.selectedItem.createTime * 1000) || ''
					},
					{
						label: '联系方式',
						value: this.formatMobile(this.selectedItem.mobile)
					},
					{
						label: '服务定位',
						value: this.formatAddress(this.selectedItem.addressInfo),
						alignRight: true
					},
					{
						label: '服务地址',
						value: this.formatAddress(this.selectedItem.address),
						alignRight: true
					},
					{
						label: '门牌号',
						value: this.formatHouseNumber(this.selectedItem.houseNumber),
						alignRight: true
					}
				];
			},
			// 判断是否显示报价按钮（根据订单类型）
			shouldShowQuoteButton() {
				return this.selectedItem.type === 1; // type为1时显示报价按钮
			}
		},
		methods: {
			// Input validation method
			validateInput(e) {
				let value = e.detail ? e.detail.value : e;

				// Remove all Chinese characters
				value = value.replace(/[\u4e00-\u9fa5]/g, '');

				// Only allow numbers and decimal point
				value = value.replace(/[^\d.]/g, '');

				// Handle decimal point logic
				const parts = value.split('.');
				if (parts.length > 2) {
					// If there are multiple decimal points, keep only the first
					value = parts[0] + '.' + parts.slice(1).join('');
				}

				if (parts.length === 2) {
					// If there is a decimal part, limit to two digits after the decimal point
					if (parts[1].length > 2) {
						parts[1] = parts[1].substring(0, 2);
						value = parts[0] + '.' + parts[1];
					}
				}

				// Prevent multiple zeros at the beginning (except for 0. scenarios)
				if (value.length > 1 && value.charAt(0) === '0' && value.charAt(1) !== '.') {
					value = value.substring(1);
				}

				// Update input value
				this.input = value;
			},

			// Handle quote button click
			handleQuote() {
				this.textsss();
				this.orderData = this.selectedItem;
				this.show = true;
			},

			// Subscribe message handling
			textsss() {
				const infodata = uni.getStorageSync('shiInfo') ? JSON.parse(uni.getStorageSync('shiInfo')) : {};
				if (infodata.status === 2) {
					uni.requestSubscribeMessage({
						tmplIds: this.tmplIds,
						success: (res) => {
							console.log('requestSubscribeMessage result:', res);
						},
						fail: (err) => {
							console.log('requestSubscribeMessage failed:', err);
						}
					});
				}
			},

			close() {
				this.show = false;
				this.input = '';
			},

			// Confirm quote
			async confirmBao() {
				if (this.input == '' || this.input == 0) {
					uni.showToast({
						icon: 'none',
						title: '请输入报价（不能为0哦）'
					});
					return;
				}
				const updatedOrderData = {
					orderId: this.id,
					price: this.input
				};
				try {
					const res = await this.$api.shifu.updateBao(updatedOrderData);
					console.log(res)
					if (res.data === -2) {
						this.masterModalShow = true;
					}
					if(res.data === -1){
						uni.showToast({
							icon: 'none',
							title: res.msg
						});
						return
					}
					if (res.data === -5) {
						uni.showToast({
							icon: 'none',
							title: res.msg
						});
						this.showNameIdModal = true; // Show the new modal
						return;
					}
					if (res.code === "-1") {
						uni.showToast({
							icon: 'none',
							title: res.msg
						});
						return
					} else {
						uni.showToast({
							icon: 'success',
							title: '报价成功'
						});
						this.close();
						setTimeout(() => {
							uni.navigateTo({
								url: '/shifu/master_bao_list?id=1'
							});
						}, 1000);
					}
				} catch (error) {
					uni.showToast({
						icon: 'fail',
						title: error.message || '报价失败'
					});
					this.close();
				}
			},

			// New method for handling image uploads in the tempForm
			imgUploadTemp(e) {
				console.log('imgUploadTemp event:', e);
				const { imagelist, imgtype } = e;
				this.$set(this.tempForm, imgtype, imagelist);
			},

			// New method to save name and ID info
			async saveNameIdInfo() {
				const { coachName, idCode, id_card1, id_card2 } = this.tempForm;

				if (!coachName || !idCode || id_card1.length === 0 || id_card2.length === 0) {
					uni.showToast({
						icon: 'none',
						title: '请填写所有必填项并上传照片'
					});
					return;
				}

				let p = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
				if (!p.test(idCode)) {
					uni.showToast({
						icon: 'none',
						title: '请填写正确的身份证号',
					});
					return;
				}
				let shifuid = JSON.parse(uni.getStorageSync('shiInfo'))
				let userId = (uni.getStorageSync('userId'))
				console.log(shifuid)
				console.log(userId)
				// Construct the payload for saving name and ID card information
				const payload = {
					coachName: coachName,
					idCode: idCode,
					id: shifuid.id,
					userId: userId,
					idCard: [id_card1[0].path, id_card2[0].path], // Assuming imgsize is 1, so only one image per type
				};

				try {
					const res = await this.$api.shifu.updataInfoSF(payload); // Replace with your actual API call
					console.log(res)
					if (res.code === "200") { // Assuming "0" means success
						uni.showToast({
							icon: 'success',
							title: '身份信息保存成功',
						});
						this.showNameIdModal = false;
						// You might want to re-attempt the quote submission or refresh data here
						// For now, let's just close the modal.
					} else {
						uni.showToast({
							icon: 'none',
							title: res.msg || '身份信息保存失败'
						});
					}
				} catch (error) {
					uni.showToast({
						icon: 'error',
						title: error.message || '身份信息保存失败'
					});
				}
			},

			// Jump to master settlement page
			goToSettle() {
				this.masterModalShow = false;
				uni.redirectTo({
					url: '/shifu/Settle'
				});
			},

			// Format mobile number with ***** in the middle, unless payType.payType is 7, 3, or 5
			formatMobile(mobile) {
				if (!mobile) return '';
				if (this.payType.payType) return mobile;

				return mobile;
			},
			// Format address to hide last 7 characters, unless payType.payType is 7, 3, or 5
			formatAddress(address) {
				if (!address) return '';
				if (this.payType.payType) return address;

				return address;
			},
			// Format house number with ***** in the middle, unless payType.payType is 7, 3, or 5
			formatHouseNumber(houseNumber) {
				if (!houseNumber) return '';
				if (this.payType.payType === 7) return houseNumber;

				return houseNumber;
			},
			// Format orderCode (keeping original format but removing '无')
			formatOrderCode(orderCode) {
				return orderCode || '';
			},
			// Validate if the URL(s) contain valid image URLs
			isValidImageUrl(val) {
				if (!val || typeof val !== 'string') return false;
				const urls = val.split(',').map(url => url.trim());
				const imageRegex = /^(https?:\/\/).*\.(png|jpg|jpeg|gif|bmp|webp)$/i;
				return urls.some(url => imageRegex.test(url));
			},
			// Split comma-separated image URLs
			splitImageUrls(val) {
				if (!val || typeof val !== 'string') return [];
				return val.split(',').map(url => url.trim()).filter(url => url !== '');
			},
			// Handle navigation
			godh() {
				uni.openLocation({
					latitude: Number(this.selectedItem.lat) || 0,
					longitude: Number(this.selectedItem.lng) || 0,
					scale: 18,
					name: this.selectedItem.address || '未知地址',
					address: this.selectedItem.addressInfo || '未知地址信息',
					success: () => console.log('Navigation opened'),
					fail: err => console.error('Navigation error:', err)
				});
			},
			// Fetch order details
			async getDetail() {
				try {
					const res = await this.$api.shifu.orderdetM(this.id);
					console.log('API Response:', res);
					console.log('coachStatus:', res.data.coachStatus);
					if (res.data.coachStatus === 1) {
						uni.showToast({
							title: '师傅状态在审核中',
							icon: 'none'
						});
					}
					if (res.data.coachStatus === -1 || res.data.coachStatus === 4) {
						uni.showToast({
							title: '你还不是师傅',
							icon: 'none'
						});
					}
					this.Info = res.data || {};
					this.selectedItem = {
						orderCode: res.data.orderCode || '',
						goodsName: res.data.goodsName || '',
						createTime: res.data.createTime || 0,
						mobile: res.data.mobile || '',
						addressInfo: res.data.addressInfo || '',
						address: res.data.address || '',
						houseNumber: res.data.houseNumber || '',
						lat: res.data.lat || 0,
						lng: res.data.lng || 0,
						type: res.data.type || 0, // Add order type
						id: res.data.id || this.id // Add order ID
					};
				} catch (err) {
					console.error('API Error:', err);
					uni.showToast({
						title: '获取订单详情失败',
						icon: 'none'
					});
				}
			},
			// Handle image load errors
			onImageError(url, index, imgIndex) {
				console.error(`Failed to load image: ${url}`);
				this.imageErrors.push(`${index}-${imgIndex}`);
				const urls = this.splitImageUrls(this.Info.settingOrderList[index].val);
				urls[imgIndex] = this.fallbackImage;
				this.$set(this.Info.settingOrderList[index], 'val', urls.join(','));
			},
			// Show image using uni.previewImage
			previewImage(url) {
				uni.previewImage({
					urls: this.splitImageUrls(this.Info.settingOrderList.find(item => item.val.includes(url)).val),
					current: url,
				});
			},
			// Close image modal
			closeImageModal() {
				this.showImageModal = false;
				this.currentImage = '';
			}
		},
		async onLoad(options) {
				this.$api.base.getConfig().then(res => {
							// console.log(res)
							this.getconfigs = res
							console.log(this.getconfigs.masterNotice)
						})
			// this.configInfo = uni.getStorageSync('configInfo')
			// console.log(this.configInfo)
			this.id = options.id || '';
			this.payType = uni.getStorageSync('orderdetails') || {};
			console.log('payType:', this.payType);
			console.log('payType.payType:', this.payType.payType);
			console.log('Page options:', options);
			if (!this.id) {
				console.error('No order ID provided');
				uni.showToast({
					title: '订单ID缺失',
					icon: 'none'
				});
				return;
			}
			await this.getDetail();
			this.ready = true;
		},
		onUnload() {
			uni.removeStorageSync('orderdetails');
			console.log('Removed orderdetails from local storage');
		}
	};
</script>

<style scoped lang="scss">
	.page {
		min-height: 100vh;
		background: linear-gradient(180deg, #f5f7fa 0%, #e4e7ed 100%);
		padding: 32rpx 24rpx 120rpx 24rpx;
		/* 底部增加padding为报价按钮留空间 */
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
	}

	.box {
		margin: 0 auto;
		max-width: 690rpx;
		background: #ffffff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
		padding: 32rpx;
	}

	/* Title Styling */
	.title {
		display: flex;
		align-items: center;
		font-size: 36rpx;
		font-weight: 700;
		color: #1a1a1a;
		margin-bottom: 24rpx;

		span {
			color: #1a1a1a; /* For required fields */
		}

		.title-icon {
			width: 48rpx;
			height: 48rpx;
			margin-right: 16rpx;
		}
	}

	/* Info Box Styling */
	.info-box {
		background: #fafafa;
		border-radius: 12rpx;
		padding: 24rpx;
		margin-bottom: 32rpx;
	}

	.info-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 16rpx 0;
		border-bottom: 1rpx solid #e5e7eb;

		&:last-child {
			border-bottom: none;
		}

		.label {
			font-size: 28rpx;
			font-weight: 500;
			color: #6b7280;
		}

		.value {
			font-size: 30rpx;
			font-weight: 600;
			color: #1a1a1a;
			max-width: 420rpx;
			white-space: normal;
			word-break: break-all;
			line-height: 1.4;

			&.align-right {
				text-align: right;
			}
		}
	}

	.navigation-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 180rpx;
		height: 64rpx;
		background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);
		border-radius: 32rpx;
		color: #ffffff;
		font-size: 28rpx;
		font-weight: 500;
		margin-top: 24rpx;
		margin-left: auto;

		.nav-icon {
			width: 32rpx;
			height: 32rpx;
			margin-right: 12rpx;
		}
	}

	/* Dynamic Section Styling */
	.dynamic-section {
		margin-bottom: 32rpx;
	}

	.img-box {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;
		margin-top: 24rpx;
	}

	.dynamic-image {
		width: 196rpx;
		height: 196rpx;
		border-radius: 16rpx;
		border: 1rpx solid #e5e7eb;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}

	/* Image Modal Styling */
	.image-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}

	.modal-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		max-width: 90%;
		max-height: 90%;
	}

	.modal-image {
		max-width: 100%;
		max-height: 80vh;
		border-radius: 16rpx;
	}

	.close-btn {
		margin-top: 20rpx;
		padding: 16rpx 32rpx;
		background: #ffffff;
		border-radius: 32rpx;
		font-size: 28rpx;
		color: #1a1a1a;
		font-weight: 500;
	}

	/* Text and Notes Styling */
	.text-box {
		margin-top: 24rpx;
		background: #f3f4f6;
		border-radius: 12rpx;
		padding: 16rpx 24rpx;
		font-size: 28rpx;
		color: #4b5563;
	}

	.notes-box {
		margin-top: 24rpx;
		background: #f9fafb;
		border-radius: 16rpx;
		padding: 24rpx 32rpx;
		font-size: 28rpx;
		color: #374151;
		line-height: 1.5;
		word-break: break-all;
	}

	/* Bottom quote area style */
	.bottom-quote-section {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background: #ffffff;
		border-top: 1rpx solid #e5e7eb;
		padding: 24rpx 32rpx;
		padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
		z-index: 100;
	}

	.quote-btn {
		width: 100%;
		height: 88rpx;
		background: linear-gradient(90deg, #2E80FE 0%, #5BA0FF 100%);
		border-radius: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		font-weight: 600;
		color: #ffffff;
		box-shadow: 0 4rpx 12rpx rgba(46, 128, 254, 0.3);
	}

	/* Quote popup style */
	.quote-popup-box {
		padding: 40rpx 30rpx;
		background: #ffffff;
		border-radius: 20rpx 20rpx 0 0;
		min-height: 400rpx;

		.popup-title {
			text-align: center;
			font-size: 36rpx;
			font-weight: 600;
			color: #1a1a1a;
			margin-bottom: 40rpx;
		}

		.popup-subtitle {
			font-size: 28rpx;
			font-weight: 500;
			color: #374151;
			margin-bottom: 20rpx;
		}

		.money-input {
			margin-bottom: 40rpx;
		}

		.confirm-quote-btn {
			width: 100%;
			height: 88rpx;
			background: linear-gradient(90deg, #2E80FE 0%, #5BA0FF 100%);
			border-radius: 44rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 32rpx;
			font-weight: 600;
			color: #ffffff;
			box-shadow: 0 4rpx 12rpx rgba(46, 128, 254, 0.3);
		}
	}

	.image-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		width: 100vw;
		height: 100vh;
		background: rgba(0, 0, 0, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}

	.modal-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 100vw;
		height: 100vh;
		position: relative;
	}

	.modal-image {
		width: 100vw;
		height: 100vh;
		object-fit: contain;
		/* Ensures image scales to fit without distortion */
		border-radius: 0;
		/* Remove border-radius for full-screen effect */
	}

	.close-btn {
		position: absolute;
		bottom: 20rpx;
		padding: 16rpx 32rpx;
		background: #ffffff;
		border-radius: 32rpx;
		font-size: 28rpx;
		color: #1a1a1a;
		font-weight: 500;
		z-index: 1100;
		/* Ensure button is above image */
	}

	/* Optimized styles for the name/ID modal content */
	.slot-content {
		padding: 20rpx 0;
	}

	.main_item {
		margin-bottom: 32rpx;
		padding: 0 24rpx;

		.title {
			margin-bottom: 16rpx;
			font-size: 30rpx;
			font-weight: 500;
			color: #1a1a1a;
			display: flex;
			align-items: center;

			span {
				color: #1A1A1A;
				margin-right: 8rpx;
			}
		}

		.modal-input {
			width: 100%;
			height: 80rpx;
			background: #f8f8f8;
			border: 1rpx solid #e5e7eb;
			border-radius: 12rpx;
			font-size: 28rpx;
			font-weight: 400;
			line-height: 80rpx;
			padding: 0 24rpx;
			box-sizing: border-box;
			transition: all 0.2s ease-in-out;

			&:focus {
				border-color: #2e80fe;
				background: #ffffff;
				box-shadow: 0 0 8rpx rgba(46, 128, 254, 0.2);
			}

			&:disabled {
				background: #f0f0f0;
				color: #999;
				cursor: not-allowed;
			}
		}

		.card {
			display: flex;
			justify-content: space-between;
			gap: 16rpx;

			.card_item {
				width: 48%;
				background: #f2fafe;
				border-radius: 16rpx;
				overflow: hidden;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
				transition: transform 0.2s ease-in-out;

				&:hover {
					transform: translateY(-4rpx);
				}

				.top {
					height: 180rpx;
					width: 100%;
					padding-top: 20rpx;

					.das {
						margin: 0 auto;
						width: 85%;
						height: 120rpx;
						border: 2rpx dashed #2e80fe;
						border-radius: 8rpx;
						padding: 10rpx;
						display: flex;
						align-items: center;
						justify-content: center;

						.up {
							width: 100%;
							height: 100%;
							display: flex;
							align-items: center;
							justify-content: center;
						}
					}
				}

				.bottom {
					height: 60rpx;
					width: 100%;
					background: linear-gradient(90deg, #2e80fe 0%, #5ba0ff 100%);
					font-size: 24rpx;
					font-weight: 500;
					color: #ffffff;
					text-align: center;
					line-height: 60rpx;
				}
			}
		}
	}
</style>