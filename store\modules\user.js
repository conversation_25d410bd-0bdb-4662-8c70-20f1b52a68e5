import $util from "@/utils/index.js"
import $api from "@/api/index.js"
import $store from "@/store/index.js"
import Vue from 'vue'
import { req } from '@/utils/req.js';

export default {
  state: {
    positionInfo: {},
    autograph: uni.getStorageSync('token') || '', // 初始化时从本地存储的 token 读取
    userInfo: '',
    appLogin: '',
    commonOptions: {
      coupon_atv_id: 0,
      channel_id: 0
    },
	erweima: '',
    location: {},
    wxlocation: {},
    isShowAuth: true,
    mineInfo: {},
    userPageType: 1 // 1用户，2技师
  },
  mutations: {
	  setErweima(state, value) {
	        state.erweima = value;
	      },
	      clearErweima(state) {
	        state.erweima = '';
	      },
    changeUserInfo(state, data) {
      const { lng, lat, city } = data
      Vue.set(state.userInfo, 'lng', lng)
      Vue.set(state.userInfo, 'lat', lat)
      Vue.set(state.userInfo, 'city', city)
    },
    updateUserItem(state, item) {
      let { key, val } = item
      if (key == 'userInfo') {
        let { phone = '', create_time, createTime } = val
        if (phone) {
          val.split_phone = phone.substring(0, 3) + '****' + phone.substring(7, 11)
        }

        // 处理创建时间，支持两种格式
        const timeValue = create_time || createTime
        if (timeValue) {
          try {
            // 如果是时间戳（数字）
            if (typeof timeValue === 'number') {
              val.create_date = $util.formatTime(timeValue * 1000, 'YY-M-D')
            }
            // 如果是字符串格式的时间
            else if (typeof timeValue === 'string') {
              const timestamp = new Date(timeValue).getTime()
              if (!isNaN(timestamp)) {
                val.create_date = $util.formatTime(timestamp, 'YY-M-D')
              } else {
                // 如果无法解析，直接使用原值的日期部分
                val.create_date = timeValue.split(' ')[0] || timeValue
              }
            }
          } catch (error) {
            console.warn('处理创建时间失败:', error)
            // 如果处理失败，使用当前日期
            val.create_date = $util.formatTime(Date.now(), 'YY-M-D')
          }
        }
      }
      state[key] = val
      if (['autograph', 'userInfo', 'location', 'appLogin'].includes(key)) {
        // 对于 autograph，存储到本地时使用 'token' 作为键
        uni.setStorageSync(key === 'autograph' ? 'token' : key, val)
      }
    }
  },
  actions: {
	  setErweimaAsync({ commit }, value) {
	        commit('setErweima', value);
	        // Optionally sync to storage for persistence
	        uni.setStorageSync('erweima', value);
	      },
    async getUserInfo({ commit, state }, param) {
      let data = await $api.user.userInfo()
      commit('updateUserItem', {
        key: 'userInfo',
        val: data
      })
    },
    async getMineInfo({ commit, state }, param) {
      let data = await $api.mine.index()
      let { fx_status } = data
      commit('updateUserItem', {
        key: 'mineInfo',
        val: data
      })
    },
    async getAuthUserProfile({ commit, state }, param) {
      let { nickName, avatarUrl } = param
      await $api.user.userUpdate(param)
      let data = Object.assign(state.userInfo, { nickName, avatarUrl })
      commit('updateUserItem', {
        key: 'userInfo',
        val: data
      })
    },
    async getAuthPhone({ commit, state }, { e = { detail: {} }, must = false } = {}) {
      let { encryptedData = '', iv = '' } = e.detail
      if (encryptedData && iv) {
        let phone = await $api.user.reportPhone({ encryptedData, iv })
        let data = Object.assign(state.userInfo, { phone })
        commit('updateUserItem', {
          key: 'userInfo',
          val: data
        })
        return phone
      }
    },
    async updateCommonOptions({ commit, state }, param) {
      let target = {}
      if (param.scene) {
        let res = await $api.base.getWxCodeData({ code_id: param.scene })
        target = Object.assign({}, state.commonOptions, res.data)
      } else {
        target = Object.assign({}, state.commonOptions, param)
      }
      let data = $util.pick(target, ['id', 'coupon_atv_id', 'admin_id', 'channel_id'])
      commit('updateUserItem', {
        key: 'commonOptions',
        val: data
      })
      return target
    }
  }
}