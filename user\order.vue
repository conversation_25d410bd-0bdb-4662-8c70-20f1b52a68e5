<template>
	<view class="page">
		<tabbar :cur="2"></tabbar>
		<u-modal :show="show" title="删除商品" content='确认要删除该订单吗' showCancelButton @confirm="confirmDel"
			@cancel="show=false"></u-modal>
		<!-- <u-picker :show="show" :columns="columns" @cancel="show = false" @confirm="confirm"></u-picker> -->
	<!-- 	<u-empty mode="car" icon="http://cdn.uviewui.com/uview/empty/car.png" v-if="list.length == 0">
		</u-empty> -->
		<view class="">
			<view class="car_item" v-for="(item,index) in list" :key="index">
				<view class="trash" @click="goTrash(item)"><u-icon name="trash" color="#2979ff" size="26"></u-icon>
				</view>
				<view class="top">
					<image :src="item.cover" mode=""></image>
					<view class="right">
						<view class="name">{{item.title}}</view>
						<view class="">
							
						</view>
						<!-- <view class="choose" @tap="show = true">{{chooseItem}}<uni-icons type="bottom" size="12"
								color="#ADADAD"></uni-icons>
						</view> -->
						<view class="choose"></view>
						<view class="price">	
							<text v-if="item.price == 0">{{item.price}}元/台起</text>
							<text v-else>师傅报价</text>
							<u-number-box v-model="item.num" :min="1">
								<template slot="minus">
									<view @click="minus(item)"
										style="width: 70rpx;height: 60rpx;background-color: #ebecee;display: flex;justify-content: center;align-items: center;">
										<u-icon name="minus" color="#333" size="16"></u-icon>
									</view>
								</template>
								<template slot="plus">
									<view @click="plus(item)"
										style="width: 70rpx;height: 60rpx;background-color: #ebecee;display: flex;justify-content: center;align-items: center;">
										<u-icon name="plus" color="#333" size="16"></u-icon>
									</view>
								</template>
							</u-number-box>
						</view>
					</view>
				</view>
				<view class="bottom">
					<view class="btn" @click="goDown(item)">去下单</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import tabbar from "@/components/tabbar.vue"
	export default {
		components: {
			tabbar
		},
		data() {
			return {
				show: false,
				// chooseItem: '',
				// columns: [
				// 	['挂机空调维修', '挂机空调']
				// ],
				value: 1,
				list: [],
				id: ''
			}
		},
		methods: {
			// confirm(e) {
			// 	// this.chooseItem = e.value[0]
			// 	this.show = false
			// }
			confirmDel(item) {
				this.show = false
				console.log(this.id)
				this.$api.service.discar(this.id).then(res => {
					uni.showToast({
						icon: 'success',
						title: '删除成功'
					})
					setTimeout(() => {
						this.getList()
					}, 500)
				})
			},
			goTrash(item) {
				console.log(item)
				this.show = true,
					this.id = item.id
			},
			plus(item) {
				this.$api.service.addtocar({
					serviceId: item.serviceId,
						settingOrderId:item.settingOrderId,
					num: 1
				})
			},
			minus(item) {
				if (item.num == 1) return
				this.$api.service.addtocar({
					serviceId: item.serviceId,
					settingOrderId:item.settingOrderId,
					num: -1
				})
			},

			getList() {
				this.$api.service.seecar().then(res => {
					console.log(res)
					this.list = res.data.list
				})
			},
			goDown(item) {
				uni.redirectTo({
					url: `/pages/commodity_details?id=${item.serviceId}`
				})
			}
		},
		onLoad(options) {
			// this.chooseItem = this.columns[0][0]
			// this.getList()
			let token = uni.getStorageSync('token')
			console.log(111)
			if(!token || token==''){
				return
			}else{
				this.getList()
			}
		},
		onShow() {
			// 检查是否需要刷新数据
			const pages = getCurrentPages();
			const currentPage = pages[pages.length - 1];
			const options = currentPage.options || {};

			if (options.refresh) {
				console.log('检测到刷新参数，重新加载购物车数据');
				let token = uni.getStorageSync('token')
				if(token && token !== ''){
					this.getList();
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.page {
		background-color: #F8F8F8;
		height: 100vh;
		overflow: auto;
		padding: 40rpx 0;
		padding-bottom: 132rpx;

		.car_item {
			margin: 0 auto;
			width: 686rpx;
			height: 396rpx;
			background: #FFFFFF;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			margin-bottom: 20rpx;
			padding: 0 20rpx;
			position: relative;

			.trash {
				position: absolute;
				top: 30rpx;
				right: 30rpx;
			}

			.top {
				padding: 36rpx 0;
				display: flex;
				border-bottom: 2rpx solid #F2F3F6;
				;

				image {
					width: 200rpx;
					height: 200rpx;
				}

				.right {
					flex: 1;
					margin-left: 20rpx;

					.name {
						font-size: 28rpx;
						font-weight: 500;
						color: #171717;
					}

					.choose {
						padding: 0 12rpx;
						margin-top: 12rpx;
						width: fit-content;
						height: 46rpx;
						background: #F8F8F8;
						border-radius: 24rpx 24rpx 24rpx 24rpx;
						font-size: 24rpx;
						font-weight: 400;
						color: #ADADAD;
						display: flex;
						align-items: center;
					}

					.price {
						width: 100%;
						margin-top: 68rpx;
						font-size: 20rpx;
						font-weight: 500;
						color: #E72427;
						display: flex;
						justify-content: space-between;
						align-items: center;
					}
				}
			}

			.bottom {
				height: 100rpx;
				display: flex;
				justify-content: flex-end;
				align-items: center;

				.btn {
					width: 240rpx;
					height: 80rpx;
					background: #2E80FE;
					border-radius: 50rpx 50rpx 50rpx 50rpx;
					font-size: 28rpx;
					font-weight: 500;
					color: #FFFFFF;
					line-height: 80rpx;
					text-align: center;
				}
			}
		}
	}
</style>