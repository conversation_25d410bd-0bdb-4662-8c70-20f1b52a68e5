<template>
	<view class="container">
		<!-- 列表菜单 -->
		<view class="menu-item" v-for="(item, index) in menuItems" :key="index" @click="handleItemClick(item)">
			<text class="item-text">{{ item.name }}</text>
			<!-- 显示已上传的图片标志 -->
			<view class="image-status">
				<text v-if="form[item.imgField]" class="uploaded">已上传</text>
				<!-- <text v-if="!form[item.imgField]" class="uploaded">未上传</text> -->
				<text class="arrow">></text>
			</view>
		</view>

		<!-- 弹窗 -->
		<u-modal
			:show="showModal"
			:title="'开通' + selectedItem.name"
			:showCancelButton="true"
			confirmText="确定"
			cancelText="取消"
			@confirm="confirmUpload"
			@cancel="closeModal"
		>
			<view class="modal-content">
				<view class="upload-box">
					<!-- 只在没有图片时显示上传按钮 -->
					<view v-if="!getImage()" class="upload-button" @click="chooseImage">
						<view class="upload-icon">
							<text class="icon">+</text>
						</view>
						<text class="upload-text">上传证件图片</text>
					</view>
					
					<!-- 显示上传的图片 -->
					<view v-if="getImage()" class="image-preview">
						<image :src="getImage()" mode="aspectFit" style="width: 200px; height: 200px;"></image>
						<view class="delete-button" @click="imgDelete">删除</view>
					</view>
				</view>
			</view>
		</u-modal>
	</view>
</template>

<script>
export default {
	data() {
		return {
			menuItems: [
				{ name: '电工证', imgField: 'electricianImg' },
				{ name: '驾驶证', imgField: 'driverLicenseImg' },
				{ name: '制冷与空调作业证', imgField: 'workPermitImg' },
				{ name: '燃气具安装维修资质', imgField: 'maintenanceCertificateImg' },
				{ name: '高空作业A类证', imgField: 'gkzyAImg' },
				{ name: '高空作业B类证', imgField: 'gkzyBImg' },
				{ name: '开锁备案', imgField: 'ksbaImg' },
				{ name: '焊工证', imgField: 'hgzImg' },
				{ name: '弱电A类证', imgField: 'rdAImg' },
				{ name: '弱电B类证', imgField: 'rdBImg' },
				{ name: '特种行业许可证', imgField: 'tzhyhkzImg' },
				{ name: '其他', imgField: 'otherImg' }
			],
			showModal: false,
			Info: {}, // Initialize as empty object
			selectedItem: {},
			form: {
				electricianImg: '',
				driverLicenseImg: '',
				workPermitImg: '',
				maintenanceCertificateImg: '',
				gkzyAImg: '',
				gkzyBImg: '',
				ksbaImg: '',
				hgzImg: '',
				rdAImg: '',
				rdBImg: '',
				tzhyhkzImg: '',
				otherImg: '',
			},
		}
	},
	
	onLoad() {
		// Fetch master data
		this.fetchMasterData();
	},
	methods: {
		async fetchMasterData() {
			try {
				const res = await this.$api.shifu.getMaster();
				if (res) {
					// Set Info reactively
					this.$set(this, 'Info', res);
					// Initialize form with fetched image fields (single image URLs)
					Object.keys(this.form).forEach(key => {
						// Set form field to the image URL if it exists and is a valid URL, otherwise empty string
						const imageUrl = res[key] && typeof res[key] === 'string' && res[key] !== 'null' ? res[key] : '';
						this.$set(this.form, key, imageUrl);
					});
					console.log('Fetched Info:', this.Info);
					console.log('Initialized Form:', this.form);
				} else {
					console.error('No data received from getMaster');
					this.$util.showToast({
						icon: 'error',
						title: '获取用户信息失败',
					});
				}
			} catch (error) {
				console.error('Error fetching master data:', error);
				this.$util.showToast({
					icon: 'error',
					title: '网络错误，请重试',
				});
			}
		},
		handleItemClick(item) {
			this.selectedItem = item;
			this.showModal = true;
		},
		closeModal() {
			this.showModal = false;
		},
		getImage() {
			return this.form[this.selectedItem.imgField] || '';
		},
		// 选择图片方法
		chooseImage() {
			uni.chooseImage({
				count: 1, // 只能选一张图片
				sizeType: ['compressed'], // 压缩图片，与原组件保持一致
				sourceType: ['album', 'camera'], // 从相册或相机选择
				success: async (res) => {
					// 直接调用uploadImage进行上传
					await this.uploadImage(res.tempFilePaths[0]);
				},
				fail: (err) => {
					console.error('选择图片失败:', err);
				}
			});
		},
		// 上传图片到服务器
		async uploadImage(tempFilePath) {
			this.$util.showLoading({
				title: '上传中'
			});
			
			try {
				// 使用与原组件相同的上传API
				const response = await this.$api.base.uploadFile({
					filePath: tempFilePath,
					name: 'multipartFile',
					formData: {
						type: 'picture',
					},
				});

				if (response) {
					// 根据API返回设置图片路径
					const imageUrl = response; // 假设response直接是图片路径
					this.$set(this.form, this.selectedItem.imgField, imageUrl);
					
					this.$util.hideAll();
					uni.showToast({
						title: '上传成功',
						icon: 'success'
					});
				} else {
					throw new Error(response?.msg || '上传失败');
				}
			} catch (error) {
				this.$util.hideAll();
				console.error('上传失败:', error);
				uni.showToast({
					title: error.message || '上传失败，请重试',
					icon: 'none'
				});
			}
		},
		imgDelete() {
			// 清除当前图片
			this.$set(this.form, this.selectedItem.imgField, '');
			this.$util.showToast({
				title: '已删除',
				icon: 'none'
			});
		},
		async confirmUpload() {
			const currentImage = this.form[this.selectedItem.imgField];
			if (!currentImage) {
				this.$util.showToast({
					icon: 'none',
					title: '请上传证件图片',
				});
				return;
			}

			try {
				// Update only the specific image field in Info
				const updatedInfo = {
					...this.Info,
					[this.selectedItem.imgField]: currentImage, // Use single URL string
				};
				
				// Update Info reactively
				this.$set(this, 'Info', updatedInfo);
				console.log('Updated Info:', this.Info);

				// Send updated Info to API
				const res = await this.$api.shifu.updataInfoSF(this.Info);
				console.log('API Response:', res);
				if(res==="信息修改成功，请等待审核"){
					this.$util.showToast({
						title: '信息修改成功，请等待审核',
						icon: 'success',
						duration: 1500,
					});
				}
				
				this.showModal = false;
			} catch (error) {
				console.error('Upload error:', error);
				this.$util.showToast({
					icon: 'error',
					title: '上传失败，请重试',
				});
			}
		},
	}
}
</script>

<style scoped lang="scss">
.header {
	width: 750rpx;
	height: 58rpx;
	background: #fff7f1;
	line-height: 58rpx;
	text-align: center;
	font-size: 28rpx;
	font-weight: 400;
}

.container {
	background-color: #f7f9fc;
	min-height: 100vh;
	padding: 10px;
}

.menu-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12px 16px;
	background-color: #ffffff;
	border-radius: 8px;
	margin-bottom: 8px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.item-text {
	font-size: 16px;
	color: #2c3e50;
	font-weight: 500;
}

.image-status {
	display: flex;
	align-items: center;
}

.uploaded {
	color: #28a745;
	font-size: 14px;
	margin-right: 8px;
}

.arrow {
	color: #7f8c8d;
	font-size: 16px;
}

.modal-content {
	padding: 20px;
	text-align: center;
}

.upload-box {
	margin: 15px 0;
}

.upload-button {
	width: 200px;
	height: 200px;
	border: 1px dashed #cccccc;
	border-radius: 8px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin: 0 auto;
	cursor: pointer;
}

.upload-icon {
	width: 60px;
	height: 60px;
	border-radius: 30px;
	background-color: #f2f2f2;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 10px;
}

.icon {
	font-size: 36px;
	color: #999999;
}

.upload-text {
	font-size: 14px;
	color: #666666;
}

.image-preview {
	position: relative;
	width: 200px;
	margin: 0 auto;
}

.delete-button {
	position: absolute;
	bottom: -30px;
	left: 50%;
	transform: translateX(-50%);
	background-color: #ff4d4f;
	color: white;
	padding: 5px 15px;
	border-radius: 4px;
	font-size: 14px;
	cursor: pointer;
}
</style>