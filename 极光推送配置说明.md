# 极光推送配置说明

## 配置完成情况

### ✅ 已完成的配置

1. **manifest.json 配置**
   - 极光推送插件已安装（JG-JPush 和 JG-JCore）
   - Android AppKey: `f3f513aa1f906542be110008`
   - iOS AppKey: `f3f513aa1f906542be110008`
   - 渠道配置: `developer-default`
   - iOS 生产环境: `false` (测试环境)

2. **厂商推送配置**
   - OPPO: AppID `31635145`
   - VIVO: AppID `105717228`
   - 小米: AppID `2882303761520298673`

3. **App.vue 推送代码**
   - 极光推送模块初始化
   - 推送消息监听和处理
   - 连接状态监听
   - 通知权限检查和引导
   - Registration ID 获取

4. **推送测试页面**
   - 创建了 `pages/push-test.vue` 测试页面
   - 可查看连接状态和 Registration ID
   - 提供复制 Registration ID 功能
   - 权限检查功能

## 使用方法

### 1. 制作自定义调试基座
由于使用了第三方插件，需要制作自定义调试基座：
- 在 HBuilderX 中选择 "运行" -> "运行到手机或模拟器" -> "制作自定义调试基座"
- 等待基座制作完成后，使用自定义基座进行调试

### 2. 测试推送功能
1. 运行应用到真机
2. 访问推送测试页面：`/pages/push-test`
3. 复制 Registration ID
4. 在极光推送控制台发送测试推送

### 3. 极光推送控制台测试
1. 登录极光推送控制台
2. 选择对应的应用
3. 进入"推送"页面
4. 选择"推送目标"为"Registration ID"
5. 输入复制的 Registration ID
6. 编写推送内容并发送

## 推送消息格式

### 基础推送
```json
{
  "title": "推送标题",
  "content": "推送内容"
}
```

### 带跳转的推送
```json
{
  "title": "推送标题",
  "content": "推送内容",
  "extras": {
    "page": "/pages/target-page"
  }
}
```

## 注意事项

1. **权限问题**
   - Android 需要开启通知权限
   - iOS 需要用户授权通知权限
   - 应用会自动检查并引导用户开启权限

2. **测试环境**
   - 当前配置为测试环境 (`JPUSH_ISPRODUCTION_IOS: false`)
   - 正式发布时需要修改为 `true`

3. **厂商推送**
   - 已配置主流厂商推送通道
   - 可提高推送到达率

4. **调试建议**
   - 使用真机测试，模拟器可能无法正常接收推送
   - 查看控制台日志了解推送状态
   - 确保网络连接正常

## 代码回退

如果需要回退推送配置，可以：

1. **回退 App.vue**
   - 删除极光推送相关的 import 语句
   - 删除 data 中的推送相关字段
   - 删除 onLaunch 中的推送初始化代码
   - 删除 methods 中的推送相关方法

2. **回退 manifest.json**
   - 清空 `nativePlugins` 中的 JG-JPush 和 JG-JCore 配置
   - 或将相关参数设置为空字符串

3. **删除测试页面**
   - 删除 `pages/push-test.vue` 文件
   - 从 `pages.json` 中移除对应的路由配置

## 常见问题

### Q: 报错 "Cannot read properties of undefined (reading 'initJPushService')"
A: 这个错误表明极光推送模块未正确加载，解决方案：
1. **必须使用自定义调试基座**：标准基座不包含第三方插件
   - 在HBuilderX中选择"运行" -> "运行到手机或模拟器" -> "制作自定义调试基座"
   - 等待基座制作完成后使用自定义基座运行
2. **检查插件配置**：确认manifest.json中的插件配置正确
3. **真机测试**：模拟器可能无法加载原生插件
4. **重新制作基座**：如果问题持续，尝试重新制作自定义调试基座

### Q: 推送无法接收
A: 检查以下几点：
- 网络连接是否正常
- 是否使用自定义调试基座
- 通知权限是否开启
- Registration ID 是否正确

### Q: iOS 推送不到
A: 确认：
- 证书配置是否正确
- 生产环境配置是否匹配
- Bundle ID 是否一致

### Q: Android 厂商推送失效
A: 检查：
- 厂商推送参数是否正确
- 应用签名是否匹配
- 厂商推送服务是否开启

### Q: 在H5或小程序中运行报错
A: 极光推送只支持APP平台，代码已添加平台判断，在其他平台会自动跳过
