import {
	req
} from '../../utils/req.js';
export default {
	// 个人中心页面
	index(param) {
		return req.get("/massage/app/IndexUser/index", param)
	},
	// 认证技师
	attestationCoach(param) {
		return req.post("/massage/app/IndexUser/attestationCoach", param)
	},
	//申请分销商
	applyReseller(param) {
		return req.post("/massage/app/IndexUser/applyReseller", param)
	},
	//分销商详情
	resellerInfo(param) {
		return req.get("/massage/app/IndexUser/resellerInfo", param)
	},
	//我的收益
	capCashInfo(param) {
		return req.get("/massage/app/IndexUser/userCashInfo", param)
	},
	//申请提现
	// applyWallet(param) {
	// 	return req.post("pay/withdrawal", param)
	// },
		applyWallet(param) {
		return req.post("core/wallet/apply", param)
	},
	//提现记录列表
	walletList(param) {
		return req.get("core/wallet/list", param)
	},
	walletStatSimple(param) {
		return req.get("core/wallet/stat/simple", param)
	},
	//提现记录详情
	walletDetail(param) {
		return req.get(`core/wallet/detail/${param}`)
	},
	//提现实际请求
	withdrawalRequest(param) {
		return req.post("../pay/withdrawal", param)
	},
	//提现实际请求
	withdrawalRequest(param) {
		return req.post("../pay/withdrawal", param)
	},
	//撤销提现申请
	cancelStatSimple(param) {
		return req.post(`core/wallet/cancel?id=${param}`)
	},
	//我的团队
	myTeam(param) {
		return req.get("/massage/app/IndexUser/myTeam", param)
	},
	//我的团队
	userCommQr(param) {
		return req.get("/massage/app/IndexUser/userCommQr", param)
	},
	//绑定及技师
	adminCoachQr(param) {
		return req.get("/massage/app/IndexUser/adminCoachQr", param)
	},
	// 获取默认地址
	// getDefultAddress(param) {
	// 	return req.get("/massage/app/IndexUser/getDefultAddress", param)
	// },
	getDefultAddress(param) {
			return req.get("address/getDefaultAddress", param)
		},
	// 地址列表
	// addressList(param) {
	// 	return req.get("/massage/app/IndexUser/addressList", param)
	// },
	// addressList(param) {
	// 	const {
		
	// 		pageNum =1, pageSize =10
	// 	} = param;
	// 	return req.get(`address/addressList?pageNum=${pageNum}&pageSize=${pageSize}`)
	// },
	addressList(param) {
		const {
		
			pageNum =1, pageSize =10
		} = param;
		return req.get(`user/address/list?pageNum=${pageNum}&pageSize=${pageSize}`)
	},
	// 地址详情
	addressInfo(param) {
		return req.get("/massage/app/IndexUser/addressInfo", param)
	},
	// 新增地址
	// addressAdd(param) {
	// 	return req.post("address/addressAdd", param)
	// },
	addressAdd(param) {
		return req.post("address/addressAdd", param)
	},
	// 修改地址
	addressUpdate(param) {
		return req.post("address/addressUpdate", param)
	},
	// 删除地址
	addressDel(param) {
		return req.delete(`address/addressDel/${param}`, param)
	},
	// 收藏技师
	coachCollectList(param) {
		return req.get("/massage/app/IndexUser/coachCollectList", param)
	},
	// 新增收藏
	addCollect(param) {
		return req.post("/massage/app/IndexUser/addCollect", param)
	},
	// 删除收藏
	delCollect(param) {
		return req.post("/massage/app/IndexUser/delCollect", param)
	},
	//卡券列表
	userCouponList(param) {
		return req.get("/massage/app/IndexUser/userCouponList", param)
	},
	//删除卡券
	couponDel(param) {
		return req.post("/massage/app/IndexUser/couponDel", param)
	},
	//卡券活动
	couponAtvInfo(param) {
		return req.post("/massage/app/IndexUser/couponAtvInfo", param)
	},
	//卡券二维码
	atvQr(param) {
		return req.post("/massage/app/IndexUser/atvQr", param)
	},
	//储值充值卡列表
	cardList(param) {
		return req.get("/massage/app/IndexBalance/cardList", param)
	},
	//充值余额(card_id)
	payBalanceOrder(param) {
		return req.post("/massage/app/IndexBalance/payBalanceOrder", param)
	},
	//充值订单列表(时间筛选 start_time,end_time)
	balaceOrder(param) {
		return req.get("/massage/app/IndexBalance/balaceOrder", param)
	},
	//消费明细
	payWater(param) {
		return req.get("/massage/app/IndexBalance/payWater", param)
	},
	//佣金明细
	commList(param) {
		return req.get("/massage/app/IndexUser/commList", param)
	},
	//获取是否能选择 公交/地铁
	getIsBus(param) {
		return req.get("/massage/app/IndexOrder/getIsBus", param)
	},
	subBxList(param) {
		return req.post("/massage/app/IndexOrder/afterSales", param)
	},
	getBxList(param) {
		return req.get("/massage/app/IndexUser/afterSales", param)
	},
	getUserBxList(param) {
		return req.get("sales/list", param)
	},
}
