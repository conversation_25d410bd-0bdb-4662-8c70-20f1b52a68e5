<template>
	<view class="page">
		<view class="main">
			<!-- Order list with dynamic rendering -->
			<view class="main_item_already" v-for="(item, index) in list" :key="index">
				<!-- Display order status based on payType -->
				<view class="title">{{ item.payType == -2 ? '等待客户选择' : '' }}</view>
				<view class="title">{{ item.payType == -1 ? '客户已取消订单' : '' }}</view>
				<view class="title">{{ item.payType == 1 ? '客户已选择报价' : '' }}</view>

				<!-- Indicate that a bid has been placed -->
				<view class="ok">您已报价</view>
				<!-- Display order number -->
				<view class="no">单号：{{ item.orderCode }}</view>
				<!-- Order details: image and name -->
				<view class="mid">
					<view class="lef">
						<image :src="item.goodsCover" mode=""></image>
						<text>{{ item.goodsName }}</text>
					</view>
				</view>
				<!-- Order timestamp -->
				<view class="bot">
					<text>{{ $util.timestampToTime(item.orderTime * 1000) }}</text>
				</view>
				<!-- Service provider info and bid price -->
				<view class="shifu">
					<scroll-view scroll-x="true">
						<view class="shifu_item">
							<view class="top">
								<image :src="userInfo.avatarUrl?userInfo.avatarUrl:'/static/mine/default_user.png'" mode=""></image>
								<view class="info">
									<view class="name">{{ userInfo.nickName }}</view>
								</view>
							</view>
							<text>￥{{ item.price }}</text>
						</view>
					</scroll-view>
				</view>
				<!-- Buttons for canceling or re-bidding, shown only if payType is -2 (awaiting selection) -->
				<view class="btnbox" v-if="item.payType == -2">
					<view class="btn can" @click="cancelBao(item)">取消报价</view>
					<view class="btn re" @click="againBao(item)">重新报价</view>
				</view>
			</view>

			<!-- Loading status indicator -->
			<view class="loading-status" v-if="loadingStatus">
				<text>{{ loadingText }}</text>
			</view>

			<!-- Empty data message -->
			<view class="empty-data" v-if="list.length === 0 && !loadingStatus">
				<text>暂无数据</text>
			</view>
		</view>

		<!-- Popup for re-bidding -->
		<u-popup :show="show" :round="10" closeable @close="close" :adjust-position="true" mode="bottom">
			<scroll-view scroll-y="true" class="popup-scroll" :scroll-into-view="scrollToId">
				<view class="box" id="input-box">
					<view class="title">重新报价</view>
					<view class="title2">报价金额</view>
					<view class="money">
						<u--input id="price-input" placeholder="请输入报价金额" prefixIcon="rmb"
							prefixIconStyle="font-size: 22px;color: #909399" type="digit" v-model="input"
							focus></u--input>
					</view>
					<view class="btn" @click="confirmBao">确认报价</view>
				</view>
			</scroll-view>
		</u-popup>

		<!-- Modal for confirming bid cancellation -->
		<u-modal :show="showCancel" title="取消报价" content="确定要取消对本单的报价吗？" showCancelButton @cancel="cancelModal"
			@confirm="confirm"></u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				page: 1, // Current page number
				pageSize: 10, // Number of items per page
				list: [], // List of orders
				show: false, // Controls re-bid popup visibility
				input: '', // Re-bid input value
				shifuId: '', // Service provider ID
				id: '', // Current order ID for actions
				userInfo: '', // User information
				showCancel: false, // Controls cancel modal visibility
				scrollToId: '', // ID for scrolling in popup
				loadingStatus: false, // Loading state flag
				loadingText: '', // Loading message
				hasMore: true, // Whether more data is available
				total: 0, // Total number of orders
				tabId:"0",
				isRefreshing: false, // Prevents multiple refreshes
				isLoadingMore: false // Prevents multiple load more requests
			};
		},
		// Handle pull-down refresh
		onPullDownRefresh() {
			console.log('refresh');
			this.refreshData();
		},
		// Handle reaching the bottom of the page
		onReachBottom() {
			console.log('reach bottom');
			if (this.list.length < this.total && !this.isLoadingMore) {
				this.page += 1;
				this.loadMoreData();
			}
		},
		// Handle page show
		onShow() {
			console.log('page show');
		},
		// onUnload() {
		// 	uni.setStorageSync('refreshReceiving', true);
		// 	this.$store.commit('setRefreshReceiving', true);
		// 		uni.navigateBack({
		// 			delta:1
		// 		})
		// 	},
		methods: {
			// Refresh the order list
			refreshData() {
				if (this.isRefreshing) return;

				this.isRefreshing = true;
				this.page = 1;
				this.list = [];
				this.hasMore = true;
				this.loadingStatus = true;
				this.loadingText = '正在刷新...';

				this.getList(false).finally(() => {
					this.isRefreshing = false;
					this.loadingStatus = false;
					uni.stopPullDownRefresh();
				});
			},

			// Load more orders
			loadMoreData() {
				if (this.isLoadingMore || !this.hasMore) return;

				this.isLoadingMore = true;
				this.loadingStatus = true;
				this.loadingText = '正在加载更多...';

				this.getList(true).finally(() => {
					this.isLoadingMore = false;
					this.loadingStatus = false;
				});
			},

			// Fetch order list from API
			getList(append = false) {
				return new Promise((resolve, reject) => {
					this.$api.shifu.masterBaolist({
						coachId: this.shifuId,
						pageNum: this.page,
						pageSize: this.pageSize
					}).then(res => {
						console.log(res);
						if (!res.data) {
							this.total = 0;
							this.list = append ? [...this.list] : [];
							this.hasMore = false;
							this.loadingText = '没有数据';
							this.loadingStatus = true;
							setTimeout(() => {
								this.loadingStatus = false;
							}, 1500);
							resolve(res);
							return;
						}
						// Update total count
						this.total = res.data.totalCount || 0;

						// Sort new data by order time (descending)
						const newList = (res.data.list || []).sort((a, b) => b.orderTime - a.orderTime);

						if (append) {
							// Append new data to existing list
							this.list = [...this.list, ...newList];
						} else {
							// Replace list with new data
							this.list = newList;
						}

						// Check if more data is available
						this.hasMore = this.list.length < this.total && newList.length === this.pageSize;

						// Show message if no more data
						if (!this.hasMore && this.list.length > 0) {
							this.loadingText = '没有更多数据了';
							this.loadingStatus = true;
							setTimeout(() => {
								this.loadingStatus = false;
							}, 1500);
						}

						resolve(res);
					}).catch(e => {
						console.error('获取列表失败:', e);

						// Roll back page number on load more failure
						if (append && this.page > 1) {
							this.page -= 1;
						}

						// Show error toast
						const errorMsg = typeof e === 'string' ? e : e.message || '请成为师傅';
						uni.showToast({
							icon: 'none',
							title: errorMsg
						});

						reject(e);
					});
				});
			},

			// Open cancel bid modal
			cancelBao(item) {
				this.showCancel = true;
				this.id = item.orderId;
			},

			// Close cancel modal
			cancelModal() {
				this.showCancel = false;
			},

			// Confirm bid cancellation
			confirm() {
				this.showCancel = false;
				this.loadingStatus = true;
				this.loadingText = '正在取消...';

				this.$api.shifu.updateQuxiaoBao({
					orderId: this.id,
				}).then(res => {
					console.log(res)
					if(res.code==="-1"){
						uni.showToast({
							icon: 'none',
							title: res.msg
						});
						return
					}else{
						uni.showToast({
							icon: 'success',
							title: '取消成功'
						});
					}
				
					// Refresh data
					this.refreshData();
				}).catch(e => {
					this.loadingStatus = false;
					uni.showToast({
						icon: 'none',
						title: typeof e === 'string' ? e : e.message || '取消失败，请重试'
					});
				});
			},

			// Open re-bid popup
			againBao(item) {
				this.show = true;
				this.id = item.orderId;
				this.scrollToId = 'input-box';
				// Ensure scrolling after popup renders
				setTimeout(() => {
					this.scrollToId = 'input-box';
				}, 100);
			},

			// Close re-bid popup
			close() {
				this.show = false;
				this.input = '';
				this.scrollToId = '';
			},

			// Submit new bid
			confirmBao() {
				if (this.input === '' || this.input == 0) {
					uni.showToast({
						icon: 'none',
						title: '请输入报价(不能为0哦)'
					});
					return;
				}

				// Validate input as a positive number
				const price = parseFloat(this.input);
				if (isNaN(price) || price <= 0) {
					uni.showToast({
						icon: 'none',
						title: '请输入有效的报价金额'
					});
					return;
				}

				this.loadingStatus = true;
				this.loadingText = '正在提交报价...';

				this.$api.shifu.updateBao({
					orderId: this.id,
					price: this.input,
				}).then(res => {
					console.log(res)
					if (res.code === "-1") {
						uni.showToast({
							icon: 'error',
							title: res.msg
						});

					} else {
						console.log(res)
						uni.showToast({
							icon: 'success',
							title: '报价成功'
						});
						this.close();
						// Refresh data
						this.refreshData();
					}
				}).catch(e => {
					this.loadingStatus = false;
					uni.showToast({
						icon: 'none',
						title: typeof e === 'string' ? e : e.message || '报价失败，请重试'
					});
					this.close();
				});
			}
		},
		// onUnload() {
		//   uni.setStorageSync('refreshReceiving', true);
		// },
		onUnload() {
		
			if(this.tabId==="1"){
				// uni.setStorageSync('refreshReceiving', true);
				uni.$emit('refreshReceivingList');
						uni.navigateBack({
							delta: 1
						})
			}
			
		},
		// Initialize data on page load
		onLoad(option) {
			console.log(option)
			this.tabId=option.id
			try {
				let shiInfo = uni.getStorageSync('shiInfo') || '';
				if (shiInfo) {
					const parsedInfo = JSON.parse(shiInfo);
					this.shifuId = parsedInfo.id;
					console.log('师傅ID:', this.shifuId);
				}

				this.userInfo = uni.getStorageSync('userInfo') || {};
				console.log('用户信息:', this.userInfo);

				// Initial data load
				this.loadingStatus = true;
				this.loadingText = '正在加载...';
				this.getList().finally(() => {
					this.loadingStatus = false;
				});
			} catch (error) {
				console.error('初始化失败:', error);
				uni.showToast({
					icon: 'none',
					title: '初始化失败'
				});
			}
		}
	};
</script>

<style scoped lang="scss">
	.page {
		background-color: #F8F8F8;
		min-height: 100vh;

		.popup-scroll {
			max-height: 60vh;
		}

		.box {
			padding: 40rpx 30rpx;

			.title {
				text-align: center;
				font-size: 32rpx;
				font-weight: 500;
				color: #171717;
			}

			.title2 {
				margin-top: 32rpx;
				margin-bottom: 20rpx;
				font-size: 24rpx;
				font-weight: 400;
				color: #171717;
			}

			.money {
				margin-bottom: 20rpx;
			}

			.btn {
				margin: 0 auto;
				margin-top: 42rpx;
				width: 688rpx;
				height: 98rpx;
				background: #2E80FE;
				border-radius: 12rpx;
				line-height: 98rpx;
				text-align: center;
				font-size: 32rpx;
				font-weight: 500;
				color: #FFFFFF;
			}
		}

		.main {
			padding: 40rpx odan30rpx;

			.main_item_already {
				padding: 28rpx 36rpx;
				background-color: #fff;
				border-radius: 24rpx;
				margin-bottom: 20rpx;

				.title {
					font-size: 40rpx;
					font-weight: 600;
					color: #333333;
				}

				.ok {
					margin-top: 20rpx;
					font-size: 24rpx;
					font-weight: 400;
					color: #E72427;
				}

				.no {
					margin-top: 20rpx;
					font-size: 24rpx;
					font-weight: 400;
					color: #999999;
					max-width: 500rpx;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}

				.mid {
					margin-top: 20rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.lef {
						display: flex;
						align-items: center;

						image {
							width: 120rpx;
							height: 120rpx;
						}

						text {
							font-size: 28rpx;
							font-weight: 400;
							color: #333333;
							margin-left: 30rpx;
							max-width: 350rpx;
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
						}
					}
				}

				.bot {
					margin-top: 20rpx;
					font-size: 24rpx;
					font-weight: 400;
					color: #999999;
				}

				.shifu {
					margin-top: 20rpx;

					scroll-view {
						width: 100%;
						white-space: nowrap;

						.shifu_item {
							display: inline-block;
							margin-right: 28rpx;

							.top {
								display: flex;

								image {
									width: 92rpx;
									height: 92rpx;
									border-radius: 50%;
									margin-right: 20rpx;
								}

								.info {
									.name {
										font-size: 28rpx;
										font-weight: 500;
										color: #333333;
									}
								}
							}

							text {
								font-size: 22rpx;
								font-weight: 500;
								color: #E72427;
								text-align: center;
							}
						}
					}
				}

				.btnbox {
					display: flex;
					justify-content: space-between;
					margin-top: 52rpx;

					.btn {
						width: 294rpx;
						height: 82rpx;
						line-height: 82rpx;
						text-align: center;
						border-radius: 12rpx;
						font-size: 32rpx;
						font-weight: 500;
						color: #2E80FE;
					}

					.can {
						border: 2rpx solid #2E80FE;
					}

					.re {
						background: #2E80FE;
						color: #FFFFFF;
					}
				}
			}

			// Loading status styles
			.loading-status {
				text-align: center;
				padding: 40rpx 0;
				color: #999999;
				font-size: 28rpx;
			}

			// Empty data styles
			.empty-data {
				text-align: center;
				padding: 200rpx 0;
				color: #999999;
				font-size: 32rpx;
			}
		}
	}
</style>