<template>
  <view class="container">
    <view class="header-decoration">
      <view class="decoration-circle circle-1"></view>
      <view class="decoration-circle circle-2"></view>
      <view class="decoration-circle circle-3"></view>
    </view>

    <view class="user-info">
      <view class="status-section">
        <view class="status-container">
          <view class="status-info">
            <text class="status-label">开启接单</text>
            <text class="status-desc">关闭后不可接收订单消息通知</text>
          </view>
          <u-switch v-model="messagePush" @change="change" active-color="#599eff"></u-switch>
        </view>
      </view>

      <view class="button-group">
        <button class="action-button secondary-button" @click="set">
          <text class="button-icon">️</text>
          <text class="button-text">系统设置</text>
        </button>
      </view>
    </view>

    <u-modal
      :show="showSubscribeModal"
      title="订阅消息提示"
      content="您尚未开启接收订单通知。请前往设置页面开启，以便及时获取新订单消息。"
      showCancelButton
      @confirm="goToSubscriptionSettings"
      @cancel="showSubscribeModal = false"
    ></u-modal>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userInfo: {
        avatarUrl: '', // Store the temporary or uploaded avatar URL
      },
      localNickName: '微信用户', // Store the temporary nickname
      originalUserInfo: {
        avatarUrl: '',
        nickName: '',
      },
      shifuid: '',
      messagePush: false,
      value: false, // 师傅状态开关
      showSubscribeModal: false, // 控制订阅消息弹窗显示
    };
  },
  onLoad() {
    this.loadUserInfo();
    this.getSInfo();
  },
  methods: {
    async getSInfo() {
      try {
        const res = await this.$api.shifu.getSInfo();
        if (res.messagePush === -1) {
          this.messagePush = false;
        } else {
          this.messagePush = true;
        }
        console.log(res);
      } catch (error) {
        console.error('Failed to get shifu info:', error);
      }
    },
    set() {
      uni.openSetting({
        success(res) {
          console.log(res);
        },
      });
    },
    async change(val) {
      console.log('师傅状态变更:', val);
      this.value = val;

      if (val) {
        // If enabling, check subscription settings
        try {
          const settingRes = await wx.getSetting({ withSubscriptions: true });
          let hasAcceptedSubscription = false;

          if (settingRes.subscriptionsSetting.itemSettings) {
            const obj = settingRes.subscriptionsSetting.itemSettings;
            // Check if *any* subscription is 'accept'
            hasAcceptedSubscription = Object.keys(obj).some((key) => obj[key] === 'accept');
          }

          if (!hasAcceptedSubscription) {
            this.showSubscribeModal = true;
            this.messagePush = false; // Revert switch if no subscription is accepted
            return; // Stop here, wait for user action on modal
          }
        } catch (error) {
          console.error('Error getting subscription settings:', error);
          uni.showToast({ title: '获取订阅设置失败', icon: 'error' });
          this.messagePush = false; // Revert switch on error
          return;
        }
      }

      // Proceed with updating message push status
      let messagePushStatus = this.value ? 0 : -1;
      try {
        const res = await this.$api.shifu.updateMessagePush({
          id: this.shifuid,
          messagePush: messagePushStatus,
        });
        if (res.code === '200') {
          uni.showToast({ title: res.data, icon: 'success' });
        } else {
          uni.showToast({ title: '请稍后重试', icon: 'error' });
        }
        console.log(res);
      } catch (error) {
        console.error('Failed to update message push status:', error);
        uni.showToast({ title: '更新失败，请稍后重试', icon: 'error' });
      }
    },
    goToSubscriptionSettings() {
      this.showSubscribeModal = false;
      uni.openSetting({
        withSubscriptions: true, // This is crucial for navigating to the subscription settings
        success(res) {
          console.log('openSetting success:', res);
          // You might want to re-check the subscription status after the user returns
        },
        fail(err) {
          console.error('openSetting fail:', err);
          uni.showToast({ title: '跳转设置失败', icon: 'error' });
        },
      });
    },
    loadUserInfo() {
      const cachedUserInfos = uni.getStorageSync('shiInfo') || {};
      const cachedUserInfo = JSON.parse(cachedUserInfos);
      console.log(cachedUserInfo);
      this.shifuid = cachedUserInfo.id || '';
      this.userInfo.avatarUrl = cachedUserInfo.avatarUrl || '';
      this.localNickName = cachedUserInfo.coachName || '微信用户';
      this.originalUserInfo.avatarUrl = this.userInfo.avatarUrl;
      this.originalUserInfo.nickName = this.localNickName;
      console.log('Loaded user info:', cachedUserInfo);
    },
    onChooseAvatar(e) {
      console.log('onChooseAvatar event:', e);
      const { avatarUrl } = e.detail;
      if (avatarUrl) {
        this.userInfo.avatarUrl = avatarUrl;
        console.log('Selected avatar:', avatarUrl);
      } else {
        console.error('Failed to get avatarUrl from event detail.');
        uni.showToast({ title: '选择头像失败', icon: 'error' });
      }
    },
    onNickNameBlur(e) {
      this.localNickName = e.detail.value;
      console.log('Nickname input:', this.localNickName);
    },
    async uploadAvatarFile(tempFilePath) {
      uni.showLoading({ title: '上传中' });
      try {
        const response = await this.$api.base.uploadFile({
          filePath: tempFilePath,
          name: 'multipartFile',
          formData: {
            type: 'picture',
          },
        });

        if (response) {
          const imageUrl = response; // Assume response is the direct image URL, consistent with second code
          console.log('Avatar uploaded successfully:', imageUrl);
          uni.hideLoading();
          uni.showToast({ title: '上传成功', icon: 'success' });
          return imageUrl;
        } else {
          throw new Error('上传失败');
        }
      } catch (error) {
        uni.hideLoading();
        console.error('Upload failed:', error);
        uni.showToast({
          title: error.message || '上传失败，请重试',
          icon: 'none',
        });
        throw error;
      }
    },
    async saveUserInfo() {
      uni.showLoading({ title: '保存中...' });
      try {
        let avatarUrl = this.userInfo.avatarUrl;
        // If avatar has changed, upload it first
        if (avatarUrl && avatarUrl !== this.originalUserInfo.avatarUrl) {
          avatarUrl = await this.uploadAvatarFile(avatarUrl);
          this.userInfo.avatarUrl = avatarUrl; // Update with uploaded URL
        }

        // Prepare updated user info
        const updatedUserInfo = {
          avatarUrl: avatarUrl || '',
          nickName: this.localNickName,
        };

        // Update backend
        console.log('Updating user info with:', updatedUserInfo);
        const res = await this.$api.user.updataInfo({
          nickName: updatedUserInfo.nickName,
          avatarUrl: updatedUserInfo.avatarUrl,
        });
        console.log('Update info response:', res);

        // Save to local storage
        uni.setStorageSync('userInfo', updatedUserInfo);
        console.log('Saved to local storage:', updatedUserInfo);

        // Update original info
        this.originalUserInfo.avatarUrl = updatedUserInfo.avatarUrl;
        this.originalUserInfo.nickName = updatedUserInfo.nickName;

        uni.hideLoading();
        uni.showToast({ title: '保存成功', icon: 'success' });

        // Navigate back or redirect
        setTimeout(() => {
          uni.navigateBack({
            delta: 1,
            success: () => console.log('Navigation back successful'),
            fail: (err) => {
              console.error('Navigation back failed:', err);
              uni.redirectTo({
                url: '/shifu/mine',
                success: () => console.log('Redirected to mine page'),
                fail: (redirectErr) => {
                  console.error('Redirect failed:', redirectErr);
                  uni.showToast({ title: '返回失败，请手动返回', icon: 'error' });
                },
              });
            },
          });
        }, 1000);
      } catch (err) {
        uni.hideLoading();
        console.error('Failed to save user info:', err);
        uni.showToast({
          title: '保存失败: ' + (err.message || '未知错误'),
          icon: 'error',
        });
      }
    },
    userOut() {
      uni.removeStorageSync('token');
      uni.removeStorageSync('userInfo');
      uni.removeStorageSync('phone');
      uni.removeStorageSync('avatarUrl');
      uni.removeStorageSync('nickName');
      // uni.removeStorageSync('shiInfo');
      uni.removeStorageSync('userId');

      // this.updateUserItem({ key: 'autograph', val: '' }); // Uncomment if you have this mutation
      // this.updateUserItem({ key: 'userInfo', val: {} }); // Uncomment if you have this mutation

      uni.showToast({ title: '已退出登录', icon: 'success', duration: 2000 });

      setTimeout(() => {
        uni.navigateBack({
          delta: 1,
          success: () => console.log('Navigation back successful'),
          fail: (err) => {
            console.error('Navigation back failed:', err);
            uni.redirectTo({
              url: '/pages/mine/mine',
              success: () => console.log('Redirected to mine page'),
              fail: (redirectErr) => {
                console.error('Redirect failed:', redirectErr);
                uni.showToast({ title: '返回失败，请手动返回', icon: 'error' });
              },
            });
          },
        });
      }, 1000);
    },
  },
};
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding-top: 40rpx;
  position: relative;
  overflow: hidden;
}

.header-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  pointer-events: none;

  .decoration-circle {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;

    &.circle-1 {
      width: 120rpx;
      height: 120rpx;
      top: -60rpx;
      right: 100rpx;
      animation: float 6s ease-in-out infinite;
    }

    &.circle-2 {
      width: 80rpx;
      height: 80rpx;
      top: 50rpx;
      right: 300rpx;
      animation: float 4s ease-in-out infinite reverse;
    }

    &.circle-3 {
      width: 60rpx;
      height: 60rpx;
      top: 20rpx;
      left: 80rpx;
      animation: float 5s ease-in-out infinite;
    }
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20rpx); }
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  // background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  width: 90%;
  padding: 60rpx 40rpx;
  border-radius: 30rpx;
  margin-top: 80rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    // background: linear-gradient(90deg, #599eff, #667eea, #764ba2);
    border-radius: 30rpx 30rpx 0 0;
  }
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;

  .avatar-wrapper {
    position: relative;
    width: 180rpx;
    height: 180rpx;
    margin-bottom: 20rpx;

    .choose-avatar-button {
      width: 100%;
      height: 100%;
      display: block;
      margin: 0;
      border-radius: 50%;
      border: none;
      background-color: transparent;
      overflow: hidden;
      line-height: normal;
      position: relative;
      box-shadow: 0 10rpx 30rpx rgba(89, 158, 255, 0.3);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }

      &::after {
        border: none;
      }

      .avatar {
        width: 100%;
        height: 100%;
        display: block;
        border: 4rpx solid #fff;
        box-sizing: border-box;
        transition: all 0.3s ease;
      }

      .avatar-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
        border-radius: 50%;

        .camera-icon {
          font-size: 40rpx;
        }
      }

      &:hover .avatar-overlay {
        opacity: 1;
      }
    }
  }

  .avatar-tip {
    font-size: 24rpx;
    color: #999;
    text-align: center;
  }
}

.nickname-section {
  width: 100%;
  margin-bottom: 50rpx;

  .input-container {
    width: 100%;

    .input-label {
      display: block;
      font-size: 28rpx;
      color: #333;
      margin-bottom: 20rpx;
      font-weight: 600;
    }

    .input-wrapper {
      position: relative;

      .nickname-input {
        width: 100%;
        height: 80rpx;
        background-color: transparent;
        border: none;
        padding: 0 0 10rpx 0;
        font-size: 32rpx;
        color: #333;
        box-sizing: border-box;
        transition: all 0.3s ease;

        &::placeholder {
          color: #ccc;
        }

        &:focus {
          outline: none;
        }
      }

      .input-underline {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 2rpx;
        background: linear-gradient(90deg, #599eff, #667eea);
        transform: scaleX(0);
        transition: transform 0.3s ease;
        transform-origin: center;
      }

      &:focus-within .input-underline {
        transform: scaleX(1);
      }
    }
  }
}

.status-section {
  width: 100%;
  margin-bottom: 60rpx;

  .status-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f3ff 100%);
    border-radius: 20rpx;
    border: 2rpx solid rgba(89, 158, 255, 0.1);

    .status-info {
      display: flex;
      flex-direction: column;

      .status-label {
        font-size: 32rpx;
        color: #333;
        font-weight: 600;
        margin-bottom: 8rpx;
      }

      .status-desc {
        font-size: 24rpx;
        color: #666;
      }
    }
  }
}

.button-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20rpx;

  .action-button {
    width: 100%;
    height: 100rpx;
    border-radius: 50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: none;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      transition: all 0.3s ease;
      transform: translate(-50%, -50%);
    }

    &:active::before {
      width: 200%;
      height: 200%;
    }

    .button-icon {
      margin-right: 15rpx;
      font-size: 28rpx;
    }

    .button-text {
      position: relative;
      z-index: 1;
    }

    &.primary-button {
      background: linear-gradient(135deg, #599eff 0%, #667eea 100%);
      color: #fff;
      box-shadow: 0 10rpx 30rpx rgba(89, 158, 255, 0.4);

      &:active {
        transform: translateY(2rpx);
        box-shadow: 0 5rpx 15rpx rgba(89, 158, 255, 0.4);
      }
    }

    &.secondary-button {
      background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
      color: #599eff;
      border: 2rpx solid rgba(89, 158, 255, 0.2);
      box-shadow: 0 5rpx 15rpx rgba(89, 158, 255, 0.1);

      &:active {
        transform: translateY(2rpx);
        box-shadow: 0 2rpx 8rpx rgba(89, 158, 255, 0.1);
      }
    }
  }
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .user-info {
    width: 95%;
    padding: 40rpx 30rpx;
  }

  .avatar-section .avatar-wrapper {
    width: 160rpx;
    height: 160rpx;
  }
}
</style>