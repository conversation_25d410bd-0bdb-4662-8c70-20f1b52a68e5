<template>
  <view class="page">
    <view class="header">
      <view class="head">
        <view class="left">
          <image :src="userInfo.avatarUrl ? userInfo.avatarUrl : '/static/mine/default_user.png'" mode=""></image>
          <view class="name">{{ userInfo.nickName ?userInfo.nickName :userInfo.phone ||'' }}</view>
        </view>
        <view class="right">已邀请{{ userNum+shiFuNum }}</view>
      </view>
    </view>
    <view class="tabs">
      <view 
        class="tab_item" 
        :class="{ active: currentTab === 1 }" 
        @click="switchTab(1)"
      >邀请的用户 {{userNum}}</view>
      <view 
        class="tab_item" 
        :class="{ active: currentTab === 2 }" 
        @click="switchTab(2)"
      >邀请的师傅{{shiFuNum}}</view>
    </view>
    <view class="fg"></view>
    <view class="box">
      <view class="title">我邀请的</view>
      <view class="list">
        <block v-for="(item, index) in list" :key="index">
          <view style="display: flex; justify-content: space-between; align-items: center;" class="list_item">
            <view style="display: flex; justify-content: space-between; align-items: center;" class="">
              <image :src="item.avatarUrl ? item.avatarUrl : '/static/mine/default_user.png'" mode=""></image>
              <view class="info">
                <view class="nam">{{ item.nickName }}</view>
                <view class="nam">{{ item.phone }}</view>
              </view>
            </view>
            <view>
              <view style="display: flex; justify-content: center; align-items: center;" class="">
                {{item.shifu===0?"用户":"师傅"}} 
				<text style="margin-left: 30rpx;"  v-if="item.cnum>0" >邀请{{item.cnum}}人</text>
              </view>
              <view style="font-size: 24rpx;" class="">
                {{item.createTime}}
              </view>
              <view v-if="vip && item.children && item.children.length > 0" 
                    @click="toggleExpand(item)" 
                    class="expand-toggle">
                {{ item.expanded ? '折叠' : '展开' }}
              </view>
            </view>
          </view>
          <view v-if="vip && item.expanded && item.children && item.children.length > 0" class="children-list">
            <view class="child-item" v-for="(child, childIndex) in item.children" :key="childIndex">
              <view style="display: flex; justify-content: space-between; align-items: center;">
                <view style="display: flex; justify-content: space-between; align-items: center;">
                  <image :src="child.avatarUrl ? child.avatarUrl : '/static/mine/default_user.png'" mode=""></image>
                  <view class="info">
                    <view class="nam">{{ child.nickName }}</view>
                    <view class="nam">{{ child.phone }}</view>
                  </view>
                </view>
                <view>
                  <view style="display: flex; justify-content: center; align-items: center;">
                    {{child.shifu===0?"用户":"师傅"}}
                  </view>
                  <view style="font-size: 24rpx; margin-left: 30rpx;">
                    {{child.createTime}}
                  </view>
                </view>
              </view>
            </view>
          </view>
        </block>
      </view>
      <view v-if="list.length < total" class="load-more" @click="loadMore">加载更多</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      page: 1,
      userInfo: {},
      list: [],
      shiFuNum: 0,
      userNum: 0,
      total: 0, // Initialize as 0
      limit: 10,
      vip: false,
      loading: false, // Prevent multiple simultaneous requests
      currentTab: 1 // 1 for user, 2 for coach
    };
  },
  onPullDownRefresh() {
    console.log('refresh');
    // Reset to first page on pull-down refresh
    this.page = 1;

    this.list = [];
    this.currentTab = 1; // Reset to user tab
    this.getList();
    setTimeout(() => {
      uni.stopPullDownRefresh();
    }, 1000);
  },
  onReachBottom() {
    // Triggered when user scrolls to the bottom
    if (this.list.length < this.total && !this.loading) {
      this.page += 1;
      this.getList();
    }
  },
  methods: {
    getUserInfo() {
      this.$api.user.userInfo().then(res => {
        this.userInfo = res;
      });
    },
    // Switch between user and coach lists
    switchTab(tab) {
      if (this.currentTab !== tab) {
        this.currentTab = tab;
        this.page = 1;
        this.list = [];
        this.getList();
      }
    },
    getList() {
      if (this.loading) return; // Prevent multiple requests
      this.loading = true;
      this.$api.service
        .getPromoterStatistics({
          type: this.currentTab, // Use currentTab to determine user or coach
          pageNum: this.page,
          pageSize: this.limit
        })
        .then(res => {
          console.log(res);
          this.vip = res.data.vip;
          console.log(this.vip);
          console.log(res.pageInfo);
          console.log(res.pageInfo.list);
          this.userNum = res.data.userNum;
          this.shiFuNum = res.data.shiFuNum;
          this.total = res.data.pageInfo.totalCount;
          // Append new items to the list
          const newItems = res.data.pageInfo?.list || res.list || [];
          // Add 'expanded' property to each item for collapsible functionality
          const formattedNewItems = newItems.map(item => ({ ...item, expanded: false }));
          this.list = this.page === 1 ? formattedNewItems : [...this.list, ...formattedNewItems];
        })
        .finally(() => {
          this.loading = false;
        });
    },
    loadMore() {
      // Optional: Manual load more button
      if (this.list.length < this.total && !this.loading) {
        this.page += 1;
        this.getList();
      }
    },
    toggleExpand(item) {
      // Toggle the expanded state of the item
      item.expanded = !item.expanded;
    }
  },
  mounted() {
    this.getUserInfo();
    this.getList();
  }
};
</script>

<style scoped lang="scss">
.page {
  .header {
    padding: 40rpx 30rpx;

    .head {
      width: 690rpx;
      height: 186rpx;
      background: #2e80fe;
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 30rpx;

      .left {
        display: flex;
        align-items: center;

        image {
          width: 106rpx;
          height: 106rpx;
          border-radius: 50%;
        }

        .name {
          margin-left: 20rpx;
          font-size: 32rpx;
          font-weight: 500;
          color: #ffffff;
          max-width: 240rpx;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }

      .right {
        width: fit-content;
        height: 74rpx;
        background: #81b3ff;
        border-radius: 12rpx;
        line-height: 74rpx;
        text-align: center;
        padding: 0 14rpx;
        font-size: 32rpx;
        font-weight: 500;
        color: #ffffff;
      }
    }
  }

  /* Tab styles */
  .tabs {
    display: flex;
    background: #fff;
    border-radius: 20rpx;
    margin: 0 30rpx 20rpx;
    padding: 10rpx;
    
    .tab_item {
      flex: 1;
      text-align: center;
      padding: 20rpx;
      font-size: 28rpx;
      color: #666;
      border-radius: 16rpx;
      transition: all 0.3s;
      
      &.active {
        background: #2E80FE;
        color: #fff;
        font-weight: 500;
      }
    }
  }

  .fg {
    background: #f3f4f5;
    width: 100%;
    height: 20rpx;
  }

  .box {
    padding: 40rpx 30rpx;

    .title {
      font-size: 32rpx;
      font-weight: 500;
      color: #171717;
    }

    .list {
      margin-top: 42rpx;

      .list_item {
        display: flex;
        justify-content: space-between; /* Ensures space between main content and toggle */
        align-items: center;
        margin-bottom: 20rpx;
        padding-bottom: 10rpx; /* Add some padding for better visual separation */
        border-bottom: 1rpx solid #eee; /* Add a subtle separator */

        image {
          width: 104rpx;
          height: 104rpx;
          border-radius: 50%;
        }

        .info {
          margin-left: 20rpx;

          .nam {
            font-size: 28rpx;
            font-weight: 400;
            color: #171717;
            max-width: 480rpx;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          .phone {
            margin-top: 20rpx;
            font-size: 28rpx;
            font-weight: 400;
            color: #999999;
          }
        }
      }

      .expand-toggle {
        font-size: 24rpx;
        color: #2e80fe;
        margin-top: 10rpx;
        cursor: pointer;
        text-align: center;
      }

      .children-list {
        margin-left: 50rpx; /* Indent children list */
        border-left: 2rpx solid #eee; /* Visual indicator for nested list */
        padding-left: 20rpx;
        margin-top: 10rpx;

        .child-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10rpx;
          padding-bottom: 5rpx;
          border-bottom: 1rpx dotted #eee; /* Dotted line for child separators */

          image {
            width: 80rpx; /* Smaller image for children */
            height: 80rpx;
            border-radius: 50%;
          }

          .info {
            margin-left: 15rpx;

            .nam {
              font-size: 26rpx;
            }
          }
        }
      }
    }

    .load-more {
      text-align: center;
      padding: 20rpx;
      font-size: 28rpx;
      color: #2e80fe;
      cursor: pointer;
    }
  }
}
</style>