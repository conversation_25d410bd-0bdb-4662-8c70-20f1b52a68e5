<template>
	<view class="page">
		<!-- Tab switching -->
		<view class="tabs">
			<view 
				class="tab_item" 
				:class="{ active: currentTab === 1 }" 
				@click="switchTab(1)"
			>邀请的用户订单</view>
			<view 
				class="tab_item" 
				:class="{ active: currentTab === 2 }" 
				@click="switchTab(2)"
			>邀请的师傅订单</view>
		</view>
		
		<!-- Order list -->
		<view class="order_item" v-for="(item, index) in list" :key="index">
			<view class="top">
				<view class="top_left">{{ item.createTime }}</view>
				<view class="top_right" v-if="item.payType == 7">交易成功</view>
				<view class="top_right" v-if="item.payType == -1">已取消</view>
				<view class="top_right" v-if="item.payType == 1">待支付</view>
				<view class="top_right" v-if="item.payType == 3">待上门</view>
				<view class="top_right" v-if="item.payType == 5">待服务</view>
				<view class="top_right" v-if="item.payType == 6">服务中</view>
			</view>
			<view class="mid">
				<view class="mid_left">{{ item.goodsName }}</view>
			
				<view v-if="currentTab === 2" class="mid_right">￥{{ item.coachServicePrice }}</view>
				<view v-else class="mid_right">￥{{ item.payPrice }}</view>
			</view>
			<view v-if="currentTab === 1" class="bottom">用户名：{{ item.nickName ?item.nickName :item.phone }}</view>
			<view v-if="currentTab === 2" class="bottom">师傅名：{{ item.nickName ?item.nickName:item.phone}}</view>
			<view class="bottom">订单号：{{ item.orderCode }}</view>
			<view class="blue"></view>
		</view>
		<u-loadmore :status="status" />
	</view>
</template>

<script>
export default {
	data() {
		return {
			page: 1,
			list: [],
			limit: 10,
			status: 'loadmore',
			currentTab: 1 // 1 for user, 2 for coach
		}
	},
	onPullDownRefresh() {
		console.log('refresh')
		this.page = 1
		this.list = []
		this.status = 'loadmore'
		this.getList()
		setTimeout(() => {
			uni.stopPullDownRefresh()
		}, 1000)
	},
	methods: {
		// Switch between user and coach orders
		switchTab(tab) {
			if (this.currentTab !== tab) {
				this.currentTab = tab
				this.page = 1
				this.list = []
				this.status = 'loadmore'
				this.getList(tab)
			}
		},
		getList(tab) {
			// Modified API call to include isUserOrCoach parameter
			this.$api.service.getPromoterOrders({
				type: this.currentTab,
				pageNum: this.page,
				pageSize: this.limit,
			}).then(res => {
				console.log(res)
				this.list = [...this.list, ...res.data.list]
				if (res.list.length < this.limit) {
					this.status = 'nomore'
				} else {
					this.status = 'loadmore'
				}
			}).catch(err => {
				console.error('API error:', err)
				this.status = 'nomore'
			})
		}
	},
	onReachBottom() {
		if (this.status === 'nomore') return
		this.status = 'loading'
		this.page++
		this.getList()
	},
	onLoad() {
		this.getList()
	}
}
</script>

<style scoped lang="scss">
.page {
	min-height: 100vh;
	background: #f8f8f8;
	padding: 30rpx;
	
	/* Tab styles */
	.tabs {
		display: flex;
		background: #fff;
		border-radius: 20rpx;
		margin-bottom: 20rpx;
		padding: 10rpx;
		
		.tab_item {
			flex: 1;
			text-align: center;
			padding: 20rpx;
			font-size: 28rpx;
			color: #666;
			border-radius: 16rpx;
			transition: all 0.3s;
			
			&.active {
				background: #2E80FE;
				color: #fff;
				font-weight: 500;
			}
		}
	}
	
	.order_item {
		width: 690rpx;
		background: #FFFFFF;
		border-radius: 20rpx;
		padding: 22rpx 30rpx;
		position: relative;
		margin-bottom: 20rpx;
		
		.top {
			display: flex;
			justify-content: space-between;
			align-items: center;
			
			.top_left {
				font-size: 24rpx;
				font-weight: 400;
				color: #999999;
			}
			.top_right {
				font-size: 20rpx;
				font-weight: 400;
				color: #07C160;
			}
		}
		
		.mid {
			margin-top: 20rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			
			.mid_left {
				font-size: 28rpx;
				font-weight: 500;
				color: #171717;
				max-width: 450rpx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
			.mid_right {
				font-size: 28rpx;
				font-weight: 500;
				color: #171717;
			}
		}
		
		.bottom {
			margin-top: 20rpx;
			font-size: 24rpx;
			font-weight: 400;
			color: #999999;
		}
		
		.blue {
			width: 10rpx;
			height: 24rpx;
			background: #2E80FE;
			position: absolute;
			top: 86rpx;
			left: 0;
		}
	}
}
</style>