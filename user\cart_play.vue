<template>
	<view class="page">
		<u-modal :show="showChoose" :content='content'></u-modal>

		<view class="choose_time" :style="showChoose?'':'height:0'">
			<view class="head">请选择时间</view>
			<view class="close" @click="showChoose = false">
				<image src="../static/images/9397.png" mode=""></image>
			</view>
			<view class="date">
				<view class="date_item" v-for="(item,index) in dateArr" :key="index"
					:style="currentDate == index?'color:#2E80FE;':''" @tap="tapDate(item,index)">
					<view class="">{{item.str}}</view>
					<view class="">{{item.date}}</view>
					<view class="hk" :style="currentDate == index?'':'display:none;'"></view>
				</view>
			</view>
			<scroll-view scroll-y="true" class="time_all">
				<view class="time_columns">
					<view class="time_column">
						<view class="time_item" v-for="(item, index) in timeArr.slice(0, 6)" :key="index"
							v-if="item.time && item.time1 && item.time2"
							:style="item.disabled ? 'background-color:#adadad;color:#fff;' : currentTime === index ? 'background-color:#2E80FE;color:#fff;' : ''"
							@tap="tapTime(item, index)">
							{{item.time}}
						</view>
					</view>
					<view class="time_column">
						<view class="time_item" v-for="(item, index) in timeArr.slice(6)" :key="index"
							v-if="item.time && item.time1 && item.time2"
							:style="item.disabled ? 'background-color:#adadad;color:#fff;' : currentTime === index + 6 ? 'background-color:#2E80FE;color:#fff;' : ''"
							@tap="tapTime(item, index + 6)">
							{{item.time}}
						</view>
					</view>
				</view>
			</scroll-view>
			<view class="btn" @tap="confirmTime">确定预约时间</view>
		</view>

		<view class="address" @click="goUrl">
			<view class="left">
				<view class="top">
					<image src="../static/images/position.png" mode=""></image>
					<text style="color: #599eff;">{{mrAddress.address?mrAddress.address:'请先添加地址哦'}}</text>
				</view>
				<view class="bottom">{{mrAddress.address?mrAddress.userName+mrAddress.mobile:''}}</view>
			</view>
			<u-icon name="arrow-right" color="#333333" size="14"></u-icon>
		</view>

		<view class="time" @click="showChoose = true">
			<view class="left">
				<image src="../static/images/clock.png" mode=""></image>
				<text>{{conDate + (conTime ? ' ' + conTime : '')}}</text>
			</view>
			<u-icon name="arrow-right" color="#333333" size="14"></u-icon>
		</view>

		<view class="fg"></view>

		<view class="main">
			<block v-for="(group, groupIndex) in groupedCartItems" :key="groupIndex">
				<!-- 如果不是第一组，添加分隔线 -->
				<view class="service-divider" v-if="groupIndex > 0"></view>
				
				<!-- 分组标题和加急选项 -->
				<view class="group-header" v-if="group.items.length > 0">
					<view class="group-title">{{getServiceTitle(group.items[0])}}</view>
					<view class="urgent-option">
						<checkbox 
							:checked="group.urgent === 1" 
							@click="toggleGroupUrgent(group)"
							style="transform: scale(0.7);"
						/>
						<text class="urgent-text">加急</text>
					</view>
				</view>
				
				<!-- 显示该组的商品 -->
				<view class="service-items">
					<view class="main_item" v-for="(item, index) in group.items" :key="index">
						<image :src="getServiceImage(item)" mode=""></image>
						<view class="right">
							<view class="title">{{getServiceTitle(item)}}</view>
							<!-- 修改已选项显示，排除最后一个值 -->
							<view class="selected-options" v-if="item.list && item.list.length > 1">
								<text class="selected-label">已选：</text>
								<text class="selected-value" v-for="(setting, idx) in item.list.slice(0, -1)" :key="idx">
									{{ setting.val }}<text v-if="idx !== item.list.length - 2">,</text>
								</text>
							</view>
							<view class="price">
								<text>￥{{getServicePrice(item)}}/台</text>
								<u-number-box v-model="item.num" :min="1" @change="e => updateQuantity(item, e.value)"></u-number-box>
							</view>
						</view>
					</view>
				</view>
			</block>
		</view>

		<view class="fg"></view>

		
		<view class="fg"></view>

		<view class="footer">
			<view class="left">总计￥{{totalAmount}}</view>
		
			<view class="right" :class="{'disabled': isSubmitting}" @click="submitOrder">
				{{isSubmitting ? '提交中...' : '立即下单'}}
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				cartItems: [], // 购物车商品列表
				serviceMap: {}, // 服务信息映射
				mrAddress: {}, // 默认地址
				notes: '', // 服务备注
				isSubmitting: false, // 提交状态

				// 时间选择相关
				showChoose: false,
				content: '',
				currentDate: 0,
				timeArr: [
					{ disabled: false, time: '00:00-02:00', time1: '00:00:00', time2: '02:00:00' },
					{ disabled: false, time: '02:00-04:00', time1: '02:00:00', time2: '04:00:00' },
					{ disabled: false, time: '04:00-06:00', time1: '04:00:00', time2: '06:00:00' },
					{ disabled: false, time: '06:00-08:00', time1: '06:00:00', time2: '08:00:00' },
					{ disabled: false, time: '08:00-10:00', time1: '08:00:00', time2: '10:00:00' },
					{ disabled: false, time: '10:00-12:00', time1: '10:00:00', time2: '12:00:00' },
					{ disabled: false, time: '12:00-14:00', time1: '12:00:00', time2: '14:00:00' },
					{ disabled: false, time: '14:00-16:00', time1: '14:00:00', time2: '16:00:00' },
					{ disabled: false, time: '16:00-18:00', time1: '16:00:00', time2: '18:00:00' },
					{ disabled: false, time: '18:00-20:00', time1: '18:00:00', time2: '20:00:00' },
					{ disabled: false, time: '20:00-22:00', time1: '20:00:00', time2: '22:00:00' },
					{ disabled: false, time: '22:00-24:00', time1: '22:00:00', time2: '24:00:00' }
				],
				currentTime: -1,
				conDate: '选择可上门时间',
				conTime: '',
				dateArr: [],
				week: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"]
			}
		},
		computed: {
			// 按serviceId分组的购物车商品
			groupedCartItems() {
				// 创建一个Map来存储分组
				const groups = new Map();
				
				// 遍历购物车商品进行分组
				this.cartItems.forEach(item => {
					if (!groups.has(item.serviceId)) {
						groups.set(item.serviceId, {
							serviceId: item.serviceId,
							items: [],
							urgent: 0 // 默认不加急
						});
					}
					groups.get(item.serviceId).items.push(item);
				});
				
				// 将Map转换为数组返回
				return Array.from(groups.values());
			},
			
			// 计算总金额
			totalAmount() {
				let total = 0;
				this.cartItems.forEach(item => {
					const price = this.getServicePrice(item);
					total += price * item.num;
				});
				return total.toFixed(2);
			}
		},
		methods: {
			// 获取服务图片
			getServiceImage(cartItem) {
				const service = this.serviceMap[cartItem.serviceId];
				return service ? service.cover : '';
			},

			// 获取服务标题
			getServiceTitle(cartItem) {
				const service = this.serviceMap[cartItem.serviceId];
				return service ? service.title : '未知服务';
			},

			// 获取服务价格
			getServicePrice(cartItem) {
				const service = this.serviceMap[cartItem.serviceId];
				return service ? parseFloat(service.price) : 0;
			},

			// 更新数量
			async updateQuantity(item, newNum) {
				console.log('Updating quantity for item:', item.id, 'New number:', newNum);
				const originalNum = item.num; // Store original number for rollback

				// Immediately update the local item's num for quick UI feedback
				const index = this.cartItems.findIndex(i => i.id === item.id);
				if (index !== -1) {
					this.$set(this.cartItems, index, { ...this.cartItems[index], num: newNum });
				}

				try {
					const res = await this.$api.service.updatatocar({
						serviceId: item.serviceId,
						id: item.id,
						num: newNum
					});

					if (res.code === "200") {
						uni.showToast({
							icon: 'success',
							title: '数量已更新',
							duration: 500
						});
					} else {
						// If API fails, revert to original number
						if (index !== -1) {
							this.$set(this.cartItems, index, { ...this.cartItems[index], num: originalNum });
						}
						uni.showToast({
							icon: 'none',
							title: res.msg || '更新失败',
							duration: 1500
						});
					}
				} catch (err) {
					console.error('更新数量失败:', err);
					// If network request fails, revert to original number
					if (index !== -1) {
						this.$set(this.cartItems, index, { ...this.cartItems[index], num: originalNum });
					}
					uni.showToast({
						icon: 'none',
						title: '网络错误，请重试',
						duration: 1500
					});
				}
				// Force update is generally not needed if using $set correctly,
				// but can be a fallback for complex reactivity issues if they persist.
				// this.$forceUpdate();
			},

			// 获取服务详细信息
			async getServicesInfo(serviceIds) {
				try {
					for (let serviceId of serviceIds) {
						const res = await this.$api.service.getserviceInfo(serviceId);
						if (res.data) {
							this.$set(this.serviceMap, serviceId, res.data); // Use $set for reactive addition
						}
					}
				} catch (err) {
					console.error('获取服务信息失败:', err);
				}
			},

			// 获取默认地址
			async getDefaultAddress() {
				try {
					let res = await this.$api.service.getaddressDefault();
					this.mrAddress = res.data;
				} catch (err) {
					console.error('获取默认地址失败:', err);
				}
			},

			// 跳转到地址页面
			goUrl() {
				if (this.isSubmitting) return;

				console.log('goUrl triggered');
				uni.navigateTo({
					url: '../user/address',
					success: () => {
						console.log('Navigation to address page successful');
					},
					fail: (err) => {
						console.error('Navigation failed:', err);
						uni.showToast({
							icon: 'none',
							title: '导航失败，请检查页面路径',
							duration: 2000
						});
					}
				});
			},

			// 初始化时间数据
			getTime() {
				const now = new Date();
				let currentDate = new Date(now);

				for (let i = 0; i < 4; i++) {
					const month = this.addLeadingZero(currentDate.getMonth() + 1);
					const date = this.addLeadingZero(currentDate.getDate());
					const day = currentDate.getDay();
					const year = currentDate.getFullYear();

					this.dateArr.push({
						str: i === 0 ? '今天' : this.week[day],
						date: month + '-' + date,
						fullDate: `${year}-${month}-${date}`
					});

					currentDate.setDate(currentDate.getDate() + 1);
				}

				this.updateTimeAvailability(0);
			},

			// 添加前导零
			addLeadingZero(number) {
				return number < 10 ? '0' + number : number;
			},

			// 更新时间可用性
			updateTimeAvailability(dateIndex) {
				console.log('Updating time availability for dateIndex:', dateIndex);
				// Create a deep copy to ensure reactivity when modifying properties within array objects
				let newTimeArr = JSON.parse(JSON.stringify(this.timeArr));

				if (dateIndex === 0) { // 今天
					const now = new Date();
					const currentHour = now.getHours();
					const currentMinute = now.getMinutes();

					newTimeArr.forEach((item, index) => {
						if (!item.time1) {
							console.warn(`Invalid time slot at index ${index}:`, item);
							item.disabled = true;
							return;
						}
						const timeStart = parseInt(item.time1.split(':')[0]);
						const timeStartMinutes = parseInt(item.time1.split(':')[1]);
						// If current time already passed the start of the time slot, disable it
						if (currentHour > timeStart || (currentHour === timeStart && currentMinute >= timeStartMinutes)) {
							item.disabled = true;
						} else {
							item.disabled = false;
						}
					});
				} else {
					// Other dates, all time slots are available
					newTimeArr.forEach(item => {
						if (item.time1) {
							item.disabled = false;
						}
					});
				}
				this.timeArr = newTimeArr; // Assign the new array to trigger reactivity
				console.log('Updated timeArr:', this.timeArr);
			},

			// 选择日期
			tapDate(item, index) {
				this.currentDate = index;
				this.currentTime = -1; // Reset time selection
				this.conTime = ''; // Reset time display
				this.updateTimeAvailability(index);
			},

			// 选择时间
			tapTime(item, index) {
				if (!item || !item.time || item.disabled) {
					uni.showToast({
						icon: 'none',
						title: '该时间段不可选择',
						duration: 1000
					});
					return;
				}
				// Single selection logic: when selecting a new time slot, deselect others
				this.currentTime = index;
			},

			// 确认时间
			confirmTime() {
				if (this.currentTime === -1) {
					uni.showToast({
						icon: 'none',
						title: '请选择预约时间',
						duration: 1000
					});
					return;
				}
				const selectedTime = this.timeArr[this.currentTime];
				if (selectedTime.disabled) {
					uni.showToast({
						icon: 'none',
						title: '该时间段不可用',
						duration: 1000
					});
					return;
				}
				this.conDate = this.dateArr[this.currentDate].date + '(' + this.dateArr[this.currentDate].str + ')';
				this.conTime = selectedTime.time;
				this.showChoose = false;
			},
		
			// 提交订单
			submitOrder() {
				if (this.isSubmitting) return;

				if (this.conDate == '选择可上门时间') {
					uni.showToast({
						icon: 'none',
						title: '请选择时间',
						duration: 1000
					});
					return;
				}

				if (!this.mrAddress.id) {
					uni.showToast({
						icon: 'none',
						title: '请先选择地址',
						duration: 1000
					});
					return;
				}

				if (this.currentTime === -1) {
					uni.showToast({
						icon: 'none',
						title: '请选择具体时间段',
						duration: 1000
					});
					return;
				}

				this.isSubmitting = true;

				// 构建时间字符串
				const selectedDateObj = this.dateArr[this.currentDate];
				const selectedTimeObj = this.timeArr[this.currentTime];

				let dateStr = selectedDateObj.fullDate;
				let startTimeStr = `${dateStr} ${selectedTimeObj.time1}`;
				let endTimeStr = `${dateStr} ${selectedTimeObj.time2}`;

				// 按serviceId分组构建请求参数
				const cartOrderings = [];

				// 为每个分组构建一个订单项
				this.groupedCartItems.forEach(group => {
					cartOrderings.push({
						addressId: this.mrAddress.id,
						carIds: group.items.map(item => item.id), // 收集该serviceId下所有商品的id
						endTime: endTimeStr,
						serviceId: group.serviceId,
						startTime: startTimeStr,
						text: this.notes || "无", // 如果没有备注则默认为"无"
						type: 1, // 默认type为1
						urgent: group.urgent || 0 // 使用分组的urgent值
					});
				});
				
				// 构建最终请求参数
				const requestData = {
					cartOrderings: cartOrderings
				};
				
				console.log('Submitting order data:', requestData);

				// 发送请求
				this.$api.service.submitCartOrder(requestData).then(res => {
					if (res.code === "200") {
						uni.showToast({
							icon: 'success',
							title: '订单提交成功',
							duration: 1500
						});
						
						setTimeout(() => {
							this.isSubmitting = false;
							// 使用 redirectTo 跳转到订单列表页面，这样返回时不会回到当前页面
							uni.redirectTo({
								url: '../user/order_list?tab=0&refresh=1&from=cart_play'
							});
						}, 1500);
					} else {
						this.isSubmitting = false;
						uni.showToast({
							icon: 'none',
							title: res.msg || '提交失败，请重试',
							duration: 2000
						});
					}
				}).catch(err => {
					console.error('提交订单失败:', err);
					this.isSubmitting = false;
					uni.showToast({
						icon: 'none',
						title: '网络错误，请重试',
						duration: 2000
					});
				});
			},
			// 切换加急状态
			toggleUrgent(item) {
				// 如果urgent不存在，初始化为0
				if (typeof item.urgent === 'undefined') {
					this.$set(item, 'urgent', 0);
				}
				// 切换状态 0 -> 1 或 1 -> 0
				this.$set(item, 'urgent', item.urgent === 1 ? 0 : 1);
				console.log(`商品${item.id}加急状态: ${item.urgent}`);
			},
			// 切换分组加急状态
			toggleGroupUrgent(group) {
				// 切换状态 0 -> 1 或 1 -> 0
				this.$set(group, 'urgent', group.urgent === 1 ? 0 : 1);
				console.log(`服务${group.serviceId}加急状态: ${group.urgent}`);
			}
		},
		async onLoad(options) {
		console.log(options); // Check the received options
		   let ids = '';
		       if (options.ids) {
		           try {
		               ids = decodeURIComponent(options.ids); // e.g., "53,62"
		               const res = await this.$api.service.getcartinfo({ ids });
		               console.log('API response:', res);

		               // Process cart data
		               if (res && res.code === "200" && res.data && res.data.length > 0) {
		                   this.cartItems = res.data;

		                   // Get detailed information for all services
		                   const serviceIds = [...new Set(res.data.map(item => item.serviceId))];
		                   await this.getServicesInfo(serviceIds);
		               }
		           } catch (e) {
		               console.error('Failed to process ids or API call failed:', e);
		           }
		       } else {
		           console.error('No ids provided in options');
		       }

		       // Initialize other data
		       this.getDefaultAddress();
		       this.getTime();
		},

		onShow() {
			let that = this;
			uni.$once('chooseAddress', function(e) {
				that.mrAddress = e;
			});
		}
	}
</script>

<style scoped lang="scss">
.page {
	padding-bottom: 200rpx;

	::v-deep .u-popup__content {
		display: none;
	}

	::v-deep .u-number-box__plus {
		border-radius: 50%;
		width: 36rpx;
		height: 36rpx !important;
		background-color: #fff !important;
		border: 1px solid #000;

		text {
			font-size: 24rpx !important;
			line-height: 36rpx !important;
		}
	}

	::v-deep .u-number-box__minus {
		border-radius: 50%;
		width: 36rpx;
		height: 36rpx !important;
		background-color: #fff !important;
		border: 1px solid #000;

		text {
			font-size: 24rpx !important;
			line-height: 36rpx !important;
		}
	}

	::v-deep .u-number-box__minus--disabled {
		border: 1px solid #ADADAD;
	}

	::v-deep .u-number-box__input {
		background-color: #fff !important;
	}

	.service-divider {
		height: 2rpx;
		background-color: #EEEEEE;
		margin: 20rpx 0;
		width: 100%;
	}

	.choose_time {
		padding-top: 40rpx;
		width: 750rpx;
		height: 920rpx;
		background: #FFFFFF;
		border-radius: 40rpx 40rpx 0rpx 0rpx;
		opacity: 1;
		position: fixed;
		bottom: 0;
		z-index: 10088;
		transition: all 0.5s;

		.head {
			font-size: 32rpx;
			font-weight: 500;
			color: #171717;
			text-align: center;
		}

		.close {
			position: absolute;
			top: 44rpx;
			right: 32rpx;

			image {
				width: 37rpx;
				height: 37rpx;
			}
		}

		.date {
			margin-top: 40rpx;
			display: flex;
			justify-content: space-around;
			align-items: center;

			.date_item {
				text-align: center;
				font-size: 28rpx;
				font-weight: 400;
				color: #171717;

				.hk {
					margin-top: 8rpx;
					width: 100%;
					height: 6rpx;
					background: #2E80FE;
					border-radius: 4rpx 4rpx 4rpx 4rpx;
					opacity: 1;
				}
			}
		}

		.time_all {
			margin-top: 10rpx;
			width: 750rpx;
			height: 520rpx;
			background: #F7F7F7;
			padding: 20rpx 10rpx;

			.time_columns {
				display: flex;
				justify-content: space-around;

				.time_column {
					width: 330rpx;
					display: flex;
					flex-direction: column;
					gap: 10rpx;

					.time_item {
						width: 100%;
						height: 80rpx;
						background: #FFFFFF;
						border-radius: 16rpx;
						font-size: 24rpx;
						font-weight: 500;
						color: #333333;
						text-align: center;
						line-height: 80rpx;
					}
				}
			}
		}

		.btn {
			margin: 0 auto;
			margin-top: 28rpx;
			width: 686rpx;
			height: 98rpx;
			background: #2E80FE;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			opacity: 1;
			font-size: 32rpx;
			font-weight: 500;
			color: #FFFFFF;
			text-align: center;
			line-height: 98rpx;
		}
	}

	.footer {
		position: fixed;
		bottom: 0;
		width: 100%;
		height: 202rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
		background-color: #fff;

		.left {
			font-size: 40rpx;
			font-weight: 600;
			color: #E72427
		}

		.mid {
			width: fit-content;
			height: 98rpx;
			border-radius: 40rpx;
			font-size: 26rpx;
			color: #2E80FE;
			line-height: 98rpx;
			text-align: center;
			font-weight: 700;
			border: 2rpx solid #2E80FE;
			padding: 0 15rpx;
		}

		.right {
			width: 294rpx;
			height: 98rpx;
			background: #2E80FE;
			border-radius: 50rpx 50rpx 50rpx 50rpx;
			opacity: 1;
			font-size: 32rpx;
			font-weight: 500;
			color: #FFFFFF;
			line-height: 98rpx;
			text-align: center;
		}

		.disabled {
			opacity: 0.6;
			pointer-events: none;
		}
	}

	.fg {
		height: 20rpx;
		background-color: #F3F4F5;
	}

	.address {
		height: 164rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 32rpx;

		.left {
			.top {
				display: flex;
				align-items: center;

				image {
					width: 36rpx;
					height: 36rpx;
					margin-right: 20rpx;
				}

				text {
					font-size: 28rpx;
					font-weight: 500;
					color: #171717;
					max-width: 400rpx;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}

			.bottom {
				font-size: 24rpx;
				font-weight: 400;
				color: #ADADAD;
				padding-left: 56rpx;
				margin-top: 12rpx;
			}
		}
	}

	.time {
		border-top: 2rpx solid #F0F0F0;
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 120rpx;
		padding: 0 32rpx;

		.left {
			display: flex;
			align-items: center;

			image {
				width: 36rpx;
				height: 36rpx;
				margin-right: 20rpx;
			}

			text {
				font-size: 28rpx;
				font-weight: 500;
				color: #2E80FE;
			}
		}
	}

	.main {
		padding: 20rpx 32rpx;

		.service-divider {
			height: 2rpx;
			background-color: #EEEEEE;
			margin: 30rpx 0;
			width: 100%;
		}

		.group-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 20rpx;
			padding: 0 20rpx;
		}

		.group-title {
			font-size: 30rpx;
			font-weight: 600;
			color: #333;
		}

		.urgent-option {
			display: flex;
			align-items: center;
		}

		.urgent-text {
			font-size: 24rpx;
			color: #333;
			margin-left: 4rpx;
		}

		.service-items {
			background-color: #FFFFFF;
			border-radius: 12rpx;
			padding: 20rpx;
			margin-bottom: 20rpx;
			box-shadow: 0rpx 0rpx 8rpx 2rpx rgba(0, 0, 0, 0.05);
		}

		.main_item {
			display: flex;
			margin-bottom: 30rpx;
		}

		.main_item:last-child {
			margin-bottom: 0;
		}

		.main_item image {
			width: 160rpx;
			height: 160rpx;
			margin-right: 20rpx;
		}

		.main_item .right {
			flex: 1;
		}

		.main_item .title {
			font-size: 28rpx;
			font-weight: 500;
			color: #171717;
			max-width: 450rpx;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}

		.main_item .selected-options {
			margin-top: 10rpx;
			font-size: 24rpx;
			color: #ADADAD;
			display: flex;
			flex-wrap: wrap;
		}

		.main_item .selected-label {
			margin-right: 4rpx;
		}

		.main_item .selected-value {
			margin-right: 4rpx;
		}

		.main_item .price {
			margin-top: 40rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
		}

		.main_item .price text {
			font-size: 28rpx;
			font-weight: 500;
			color: #2E80FE;
		}
	}

	.notes {
		padding: 40rpx 32rpx;

		.title {
			font-size: 32rpx;
			font-weight: 500;
			color: #333333;
		}

		textarea {
			margin-top: 40rpx;
			padding: 40rpx 30rpx;
			box-sizing: border-box;
			width: 686rpx;
			height: 242rpx;
			background: #F7F7F7;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			opacity: 1;
		}
	}
}
.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.group-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.urgent-option {
  display: flex;
  align-items: center;
}

.urgent-text {
  font-size: 24rpx;
  color: #333;
  margin-left: 4rpx;
}
</style>



















