<template>
	<view class="custom-tabbar fixed flex-center bg-base border-top">
		<view
			v-for="(item, index) in tabbarConfig"
			:key="item.value"
			@tap.stop="changeTab(item.value)"
			class="flex-center flex-column"
			:style="{ width: 100 / tabbarConfig.length + '%', color: isActive(item.value) ? activeColor : inactiveColor }"
		>
			<view class="icon-wrapper">
				<u-icon
					:name="item.icon"
					:color="isActive(item.value) ? activeColor : inactiveColor"
					size="28"
				></u-icon>
			</view>
			<view class="tab-text">{{ item.name }}</view>
		</view>
	</view>
</template>
<script>
export default {
	name: 'CustomTabbar',
	props: {
		cur: {
			type: [Number, String],
			default: '0'
		}
	},
	data() {
		return {
			activeColor: '#E41F19',
			lat: '',
			count: 0,
			lng: '',
			inactiveColor: '#666',
			tmplIds: [
				'qt5Q70oqYNTB2RaTzl0peEHpjK-iinWr3ZfxGvs4wtg',
				'_2z7Bbw8oq4av-yqP31fZLaI82Z_N52wNM_1ihXQv6I',
				'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'
			],
			tabbarConfig: [
				{
					name: '接单大厅',
					icon: 'fingerprint',
					value: 0,
					path: '/shifu/Receiving'
				},
				{
					name: '我的',
					icon: 'account-fill',
					value: 1,
					path: '/shifu/mine'
				}
			]
		};
	},
	mounted() {
		this.updateTabbarHeight();
		console.log('Current tab:', this.cur);
	},
	onLoad() {
		this.count = uni.getStorageSync('listCount');
		console.log(this.count);
	},
	methods: {
		// 检查是否为激活状态的tab
		isActive(value) {
			return String(value) === String(this.cur);
		},
		// 切换tab
		changeTab(value) {
			if (this.isActive(value)) return;
			const targetTab = this.tabbarConfig.find(item => String(item.value) === String(value));
			if (targetTab) {
				console.log('Navigating to:', targetTab.path);
				uni.reLaunch({
					url: targetTab.path
				});
			}
			// let infodata = JSON.parse(uni.getStorageSync('shiInfo'));
			const shiInfoRaw = uni.getStorageSync('shiInfo');
			  if (!shiInfoRaw) {
			    console.warn('shiInfo is empty or not set in storage');
			    return;
			  }
			
			  let infodata;
			  try {
			    infodata = JSON.parse(shiInfoRaw);
			  } catch (e) {
			    console.error('Failed to parse shiInfo:', e);
			    return;
			  }
			if (infodata.status === 2) {
				const panduan = uni.getStorageSync('shiInfo');
				// Check if user has previously canceled the subscription prompt
				const hasCanceledSubscription = uni.getStorageSync('hasCanceledSubscription');
				const hasShownModal = uni.getStorageSync('hasShownSubscriptionModal');
				if (panduan && !hasCanceledSubscription) {
					console.log(1111);
					uni.requestSubscribeMessage({
						tmplIds: this.tmplIds,
						success: (res) => {
							console.log('requestSubscribeMessage success:', res, 'with tmplIds:', this.tmplIds);
							// Check if any of the template IDs were rejected
							const hasRejection = this.tmplIds.some(tmplId => res[tmplId] === 'reject');
							if (hasRejection && !hasShownModal) {
								uni.showModal({
									title: '提示',
									content: '您已关闭消息订阅，建议点击‘通知管理’开启，方便及时接收用户订单通知。',
									cancelText: '取消',
									confirmText: '去开启',
									confirmColor: '#007AFF', // Set confirm button background to blue
									success: (modalRes) => {
										// Set flag to prevent showing modal again
										uni.setStorageSync('hasShownSubscriptionModal', true);
										if (modalRes.confirm) {
											uni.openSetting({
												withSubscriptions: true
											});
										} else if (modalRes.cancel) {
											// Set flag in storage when user cancels
											uni.setStorageSync('hasCanceledSubscription', true);
										}
									}
								});
							}
						},
						fail: (err) => {
							console.error('requestSubscribeMessage failed:', err, 'with tmplIds:', this.tmplIds);
						}
					});
				}
			}
		},
		// 计算tabbar高度并更新
		updateTabbarHeight() {
			const query = uni.createSelectorQuery().in(this);
			query
				.select('.custom-tabbar')
				.boundingClientRect(data => {
					if (data) {
						const systemInfo = uni.getSystemInfoSync();
						const tabbarHeight = data.height;
						const windowHeight = systemInfo.windowHeight;
						this.$emit('update:tabbarHeight', tabbarHeight);
						this.$emit('update:availableHeight', windowHeight - tabbarHeight);
					}
				})
				.exec();
		}
	}
};
</script>
<style scoped lang="scss">
.custom-tabbar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 120rpx;
	padding-bottom: calc(env(safe-area-inset-bottom) / 2);
	background-color: #fff;
	z-index: 1000;
	border-top: 1px solid #eee;
}
.tab-text {
	font-size: 22rpx;
	margin-top: 5rpx;
	line-height: 32rpx;
}
.flex-center {
	display: flex;
	justify-content: center;
	align-items: center;
}
.flex-column {
	flex-direction: column;
}
.bg-base {
	background-color: #fff;
}
.icon-wrapper {
	position: relative;
}
.badge {
	position: absolute;
	top: -5rpx;
	right: -8rpx;
	width: 32rpx;
	height: 32rpx;
	background-color: #E41F19;
	color: #fff;
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 20rpx;
}
</style>