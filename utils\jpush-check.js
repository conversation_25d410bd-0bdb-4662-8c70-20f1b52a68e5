/**
 * 极光推送配置检查工具
 * 用于诊断极光推送配置问题
 */

// 检查极光推送配置状态
export function checkJPushConfig() {
	const result = {
		platform: '',
		moduleAvailable: false,
		configStatus: {},
		recommendations: []
	};
	
	// 检查运行平台
	// #ifdef APP-PLUS
	result.platform = 'APP-PLUS';
	
	// 检查极光推送模块是否可用
	try {
		const jpushModule = uni.requireNativePlugin("JG-JPush");
		if (jpushModule) {
			result.moduleAvailable = true;
			result.configStatus.jpushModule = '✅ 极光推送模块加载成功';
		} else {
			result.moduleAvailable = false;
			result.configStatus.jpushModule = '❌ 极光推送模块未找到';
			result.recommendations.push('请使用自定义调试基座运行应用');
		}
	} catch (error) {
		result.moduleAvailable = false;
		result.configStatus.jpushModule = '❌ 极光推送模块加载失败: ' + error.message;
		result.recommendations.push('请检查manifest.json中的插件配置');
		result.recommendations.push('请使用自定义调试基座运行应用');
	}
	
	// 检查plus环境
	if (typeof plus !== 'undefined') {
		result.configStatus.plusEnvironment = '✅ Plus环境可用';
	} else {
		result.configStatus.plusEnvironment = '❌ Plus环境不可用';
		result.recommendations.push('请在真机或自定义调试基座中运行');
	}
	
	// 检查系统信息
	try {
		const systemInfo = uni.getSystemInfoSync();
		result.configStatus.systemInfo = `✅ 系统: ${systemInfo.platform} ${systemInfo.system}`;
		result.configStatus.appVersion = `✅ 应用版本: ${systemInfo.appVersion || '未知'}`;
	} catch (error) {
		result.configStatus.systemInfo = '❌ 无法获取系统信息';
	}
	
	// #endif
	
	// #ifdef H5
	result.platform = 'H5';
	result.configStatus.platform = '⚠️ H5平台不支持极光推送';
	result.recommendations.push('极光推送仅支持APP平台，请在APP中测试');
	// #endif
	
	// #ifdef MP-WEIXIN
	result.platform = 'MP-WEIXIN';
	result.configStatus.platform = '⚠️ 微信小程序不支持极光推送';
	result.recommendations.push('极光推送仅支持APP平台，请在APP中测试');
	// #endif
	
	// #ifdef MP-ALIPAY
	result.platform = 'MP-ALIPAY';
	result.configStatus.platform = '⚠️ 支付宝小程序不支持极光推送';
	result.recommendations.push('极光推送仅支持APP平台，请在APP中测试');
	// #endif
	
	return result;
}

// 检查推送权限状态
export function checkPushPermission() {
	return new Promise((resolve) => {
		const result = {
			hasPermission: false,
			status: '',
			message: ''
		};
		
		// #ifdef APP-PLUS
		try {
			const jpushModule = uni.requireNativePlugin("JG-JPush");
			if (!jpushModule) {
				result.status = 'MODULE_NOT_FOUND';
				result.message = '极光推送模块未找到';
				resolve(result);
				return;
			}
			
			const systemInfo = uni.getSystemInfoSync();
			if (systemInfo.platform === 'ios') {
				jpushModule.requestNotificationAuthorization((res) => {
					result.status = res.status;
					switch(res.status) {
						case 0:
							result.message = '未请求权限';
							result.hasPermission = false;
							break;
						case 1:
							result.message = '权限被拒绝';
							result.hasPermission = false;
							break;
						case 2:
							result.message = '权限已授权';
							result.hasPermission = true;
							break;
						default:
							result.message = '未知状态';
							result.hasPermission = false;
					}
					resolve(result);
				});
			} else {
				jpushModule.isNotificationEnabled((res) => {
					result.status = res.code;
					result.hasPermission = res.code === 1;
					result.message = res.code === 1 ? '通知权限已开启' : '通知权限未开启';
					resolve(result);
				});
			}
		} catch (error) {
			result.status = 'ERROR';
			result.message = '检查权限时发生错误: ' + error.message;
			resolve(result);
		}
		// #endif
		
		// #ifndef APP-PLUS
		result.status = 'NOT_SUPPORTED';
		result.message = '当前平台不支持推送权限检查';
		resolve(result);
		// #endif
	});
}

// 获取Registration ID
export function getRegistrationID() {
	return new Promise((resolve) => {
		// #ifdef APP-PLUS
		try {
			const jpushModule = uni.requireNativePlugin("JG-JPush");
			if (!jpushModule) {
				resolve({
					success: false,
					registrationID: '',
					message: '极光推送模块未找到'
				});
				return;
			}
			
			jpushModule.getRegistrationID((result) => {
				resolve({
					success: true,
					registrationID: result.registerID,
					message: '获取成功'
				});
			});
		} catch (error) {
			resolve({
				success: false,
				registrationID: '',
				message: '获取失败: ' + error.message
			});
		}
		// #endif
		
		// #ifndef APP-PLUS
		resolve({
			success: false,
			registrationID: '',
			message: '当前平台不支持获取Registration ID'
		});
		// #endif
	});
}

// 生成诊断报告
export async function generateDiagnosticReport() {
	const configCheck = checkJPushConfig();
	const permissionCheck = await checkPushPermission();
	const registrationIDCheck = await getRegistrationID();
	
	const report = {
		timestamp: new Date().toLocaleString(),
		platform: configCheck.platform,
		moduleAvailable: configCheck.moduleAvailable,
		configStatus: configCheck.configStatus,
		permissionStatus: permissionCheck,
		registrationID: registrationIDCheck,
		recommendations: configCheck.recommendations,
		summary: ''
	};
	
	// 生成总结
	if (configCheck.platform !== 'APP-PLUS') {
		report.summary = '当前平台不支持极光推送，请在APP平台中测试';
	} else if (!configCheck.moduleAvailable) {
		report.summary = '极光推送模块未加载，请使用自定义调试基座运行';
	} else if (!permissionCheck.hasPermission) {
		report.summary = '极光推送模块正常，但缺少通知权限';
	} else {
		report.summary = '极光推送配置正常，可以正常使用';
	}
	
	return report;
}

// 打印诊断报告到控制台
export async function printDiagnosticReport() {
	const report = await generateDiagnosticReport();
	
	console.log('=== 极光推送诊断报告 ===');
	console.log('时间:', report.timestamp);
	console.log('平台:', report.platform);
	console.log('模块状态:', report.moduleAvailable ? '可用' : '不可用');
	console.log('');
	
	console.log('配置状态:');
	Object.entries(report.configStatus).forEach(([key, value]) => {
		console.log(`  ${key}: ${value}`);
	});
	console.log('');
	
	console.log('权限状态:', report.permissionStatus.message);
	console.log('Registration ID:', report.registrationID.registrationID || '未获取');
	console.log('');
	
	if (report.recommendations.length > 0) {
		console.log('建议:');
		report.recommendations.forEach((rec, index) => {
			console.log(`  ${index + 1}. ${rec}`);
		});
		console.log('');
	}
	
	console.log('总结:', report.summary);
	console.log('========================');
	
	return report;
}
