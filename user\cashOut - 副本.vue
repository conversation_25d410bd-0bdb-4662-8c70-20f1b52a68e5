<template>
	<view class="page">
		<view class="header">
			<view class="left">提现至</view>
			<view class="right">微信</view>
		</view>
		<view class="mid">
			<view class="title">提现金额</view>
			<view class="top">
				<view class="t_left">
					<u--input
						placeholder="请输入提现金额"
						type="digit"
						border="none"
						v-model="money"
						@change="change"
						prefixIcon="rmb"
					></u--input>
				</view>
				<view class="r_left" @tap="goAll">全部提现</view>
			</view>
			<view class="bottom">可提现金额￥{{ allmoney }}</view>
		</view>
		<view class="btn" @tap="confirmTx" :disabled="isSubmitting">确认提现</view>
		<text class="tips">温馨提示：提现申请发起后，预计3个工作日内到账。</text>
		<text class="contact">有问题请联系客服 <text class="phone" @tap="copyPhoneNumber">4008326986</text></text>
	</view>
</template>

<script>
export default {
	data() {
		return {
			money: '',
			allmoney: '0',
			isSubmitting: false,
			mchId: '1648027588', // Replace with your actual Merchant ID
		};
	},
	onPullDownRefresh() {
		console.log('refresh');
		setTimeout(function () {
			uni.stopPullDownRefresh();
		}, 1000);
	},
	methods: {
		async confirmTx() {
			const amount = Number(this.money);
			// if (!amount || amount <= 0) {
			// 	uni.showToast({
			// 		title: '请输入有效的提现金额',
			// 		icon: 'none',
			// 	});
			// 	return;
			// }
			// if (amount > Number(this.allmoney)) {
			// 	uni.showToast({
			// 		title: '超过可提现金额',
			// 		icon: 'none',
			// 	});
			// 	return;
			// }
			// if (amount > 800) {
			// 	uni.showToast({
			// 		title: '最高提现金额为799元',
			// 		icon: 'none',
			// 	});
			// 	return;
			// }
			// if (amount < 1) {
			// 	uni.showToast({
			// 		title: '最低提现金额为1元',
			// 		icon: 'none',
			// 	});
			// 	return;
			// }

			// Show confirmation modal before proceeding
			uni.showModal({
				title: '确认提现',
				content: '为确保您的账户余额准确无误，提现操作一旦提交，请不要中途退出或刷新页面，若您在提现过程中中止操作，可能会导致余额错误，需等待1-3个工作日处理您的请求。',
				confirmText: '确定',
				cancelText: '取消',
				success: async (res) => {
					console.log(res)
					if (res.confirm) {
						// Proceed with withdrawal only if user confirms
						// if (!uni.canIUse('requestMerchantTransfer')) {
						// 	uni.showModal({
						// 		content: '你的微信版本过低，请更新至最新版本。',
						// 		showCancel: false,
						// 	});
						// 	return;
						// }

						try {
							this.isSubmitting = true;
							// Request signed package from backend
							const res = await this.$api.mine.applyWallet({ amount: this.money, type: 4,cashToType: 1 });

							if(res.code==='200'){
								uni.showToast({
									title: res.msg || '提现申请提交成功',
									icon: 'success',
								});

								// 提现成功后跳转到提现记录页面
								setTimeout(() => {
									uni.navigateTo({
										url: '/user/coreWallet'
									});
								}, 1500);
							} else {
								uni.showToast({
									title: res.msg || '提现申请失败',
									icon: 'none',
								});
							}
							// Initiate WeChat transfer
							uni.requestMerchantTransfer({
								mchId: res.data.mchId,
								appId: res.data.appId,
								package: res.data.packageInfo,
								success: (transferRes) => {
									console.log(transferRes)
									if (transferRes.result === 'success') {
										uni.showToast({
											icon: 'none',
											title: '提现申请提交成功',
										});
										setTimeout(() => {
											uni.navigateTo({
												url: '/shifu/promotion'
											});
										}, 1000);
									} else {
										uni.showToast({
											title: '提现申请失败，请稍后重试',
											icon: 'none',
										});
									}
								},
								fail: (transferRes) => {
									uni.showToast({
										title: transferRes.errMsg || '提现失败，请稍后重试',
										icon: 'none',
									});
								},
								complete: () => {
									this.isSubmitting = false;
								},
							});
						} catch (error) {
							uni.showToast({
								title: '请稍后重试',
								icon: 'none',
							});
							this.isSubmitting = false;
						}
					}
				},
			});
		},
		goAll() {
			this.money = this.allmoney;
		},
		change(e) {
			// Handle input change if needed
		},
		async getMoney() {
			try {
				const res = await this.$api.service.seeTuiMoney();
				this.allmoney = res.data.totalCash || '0';
				this.money = this.allmoney;
			} catch (error) {
				uni.showToast({
					title: '获取可提现金额失败',
					icon: 'none',
				});
			}
		},
		copyPhoneNumber() {
			uni.setClipboardData({
				data: '4008326986',
				success: () => {
					uni.showToast({
						title: '客服电话已复制',
						icon: 'success',
					});
				},
				fail: () => {
					uni.showToast({
						title: '复制失败，请稍后重试',
						icon: 'none',
					});
				}
			});
		},
	},
	onLoad() {
		this.getMoney();
	},
};
</script>

<style scoped lang="scss">
.page {
	background-color: #f8f8f8;
	min-height: 100vh;
	padding: 20rpx 0;
	.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 28rpx;
		font-weight: 500;
		color: #3b3b3b;
		padding: 0 30rpx;
		width: 750rpx;
		height: 118rpx;
		background: #ffffff;
		.right {
			display: flex;
			align-items: center;
		}
	}
	.mid {
		margin-top: 20rpx;
		width: 750rpx;
		height: 276rpx;
		background: #ffffff;
		padding: 0 30rpx;
		padding-top: 40rpx;
		.title {
			font-size: 28rpx;
			font-weight: 500;
			color: #3b3b3b;
		}
		.top {
			display: flex;
			align-items: flex-end;
			justify-content: space-between;
			padding-top: 28rpx;
			padding-bottom: 20rpx;
			border-bottom: 2rpx solid #f2f3f6;
			.r_left {
				font-size: 28rpx;
				font-weight: 500;
				color: #e51837;
			}
		}
		.bottom {
			padding-top: 20rpx;
			font-size: 24rpx;
			font-weight: 500;
			color: #999999;
		}
	}
	.btn {
		margin: 60rpx auto 0;
		width: 690rpx;
		height: 98rpx;
		background: #2e80fe;
		border-radius: 50rpx;
		font-size: 32rpx;
		font-weight: 500;
		color: #ffffff;
		line-height: 98rpx;
		text-align: center;
		&:disabled {
			background: #cccccc;
		}
	}
	.tips {
		display: block;
		font-size: 24rpx;
		font-weight: 500;
		color: #999999;
		text-align: center;
		margin-top: 20rpx;
	}
	.contact {
		display: block;
		font-size: 24rpx;
		font-weight: 500;
		color: #999999;
		text-align: center;
		margin-top: 20rpx;
		.phone {
			color: #2e80fe;
			text-decoration: underline;
		}
	}
}
</style>