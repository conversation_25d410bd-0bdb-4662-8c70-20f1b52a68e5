<template>
	<view class="agreement-container">
		<view class="content-wrapper">
			<rich-text class="agreement-content" :nodes="info"></rich-text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				info:''
			}
		},
		methods: {
			
		},
		onLoad(options) {
			console.log(options)
			if(options.type==="privacy"){
				this.$api.base.getConfig().then(res=>{
					console.log(res)
					this.info=res.content
					// console.log(this.info)
				})
			} else {
				// 默认显示服务协议
				this.$api.base.getConfig().then(res=>{
					console.log(res)
					this.info=res.loginProtocol
					// console.log(this.info)
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
.agreement-container {
	min-height: 100vh;
	background-color: #f8f9fa;
	padding: 20rpx;
}

.content-wrapper {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.agreement-content {
	line-height: 1.8;
	font-size: 28rpx;
	color: #333;

	/* 处理富文本内容样式 */
	:deep(p) {
		margin-bottom: 20rpx;
		text-indent: 2em;
	}

	:deep(h1), :deep(h2), :deep(h3) {
		font-weight: bold;
		margin: 30rpx 0 20rpx;
		color: #222;
	}

	:deep(h1) {
		font-size: 36rpx;
	}

	:deep(h2) {
		font-size: 32rpx;
	}

	:deep(h3) {
		font-size: 30rpx;
	}

	:deep(ul), :deep(ol) {
		padding-left: 40rpx;
		margin-bottom: 20rpx;
	}

	:deep(li) {
		margin-bottom: 10rpx;
	}
}
</style>
