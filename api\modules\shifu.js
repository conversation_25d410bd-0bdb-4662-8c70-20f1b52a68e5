import {
	req
} from '../../utils/req.js';
export default {
	index(param) {
		return req.get(`shiFu/index/shiFuBanner`)
	},
	master_Order(param) {
		const {
			coachId,
			payType = 0,pageNum =1, pageSize =10
		} = param;
		return req.get(`shiFu/orderDetails/allOrderList?coachId=${coachId}&payType=${payType}&pageNum=${pageNum}&pageSize=${pageSize}`)

	},
	coachCash(param){
		return req.get("coach/coachInfo", param)
	},
	// nowPay(param){
	// return req.post("shiFu/service/payMargin", param)
	// },
	nowPay(param){
		const {
			payPrice,
			couponId,
			type
		} = param;
		
		return req.post(`shiFu/service/payMargin?payPrice=${payPrice}&couponId=${couponId}&type=${type}`)
	// return req.post(`shiFu/service/payMargin?payPrice=${param}`)
	},
	seeBzj(param)
	{
		return req.get("shiFu/service/margin", param)
	},
	updateMessagePush(param)
	{
		return req.post("shiFu/service/updateMessagePush", param)
	},
	incomeSee(param) {
		const {
			type,
			pageNum =1, pageSize =10
		} = param;
		return req.get(`shiFu/service/servicePrice?type=${type}&pageNum=${pageNum}&pageSize=${pageSize}`)
	},
	//师傅信息查看
	masterSee(param) {
		return req.get("/massage/app/IndexUser/coachInfo", param)
	},
	//省市区
	getCity(param) {
		return req.get(`city/citySelect?pid=${param}`)
	},
	getSInfo(param) {
		return req.get("coach/coachInfo", param)
	},
	// indexQuote(param) {
	// 	return req.get("shiFu/order/shiFuOrderList", param)
	// },
	indexQuote(param) {
		const {
			lng,
			lat,
			pageNum =1, pageSize =10,
			parentId=0
		} = param;
		
		return req.get(`shiFu/order/shiFuOrderList?lng=${lng}&lat=${lat}&parentId=${parentId}&pageNum=${pageNum}&pageSize=${pageSize}`)
	},
	// /师傅押金缴纳
	masterPayY(param) {
		return req.get("/massage/app/IndexUser/cashPledge", param)
	},
	// 师傅入驻
	masterEnter(param) {
		return req.post("coach/apply", param)
	},
	updataInfoSF(param) {
		return req.post("shiFu/service/updateInfo", param)
	},
	// 更新师傅信息
	updateBao(param) {
		return req.post("shiFu/quotation/orderQuotation", param)
	},
	// 更新技能
	updataSkill(param) {
		return req.post("shiFu/service/updateInfo", param)
	},
	//获取技能
	getSkill(param) {
		return req.get("shiFu/service/serviceCate", param)
	},
	// serviceCate(param) {
	// 	return req.get(`/massage/app/Index/serviceCate&city_id=${param}`)
	// },
	serviceCate(param) {
		return req.get("shiFu/service/serviceClassification", param)
	},
	getshifstutas(param) {
		return req.post("shiFu/index/shiFuAuth", param)
	},
	masterBaolist(param) {
		const {
			coachId,
			pageNum =1, pageSize =10
		} = param;
		return req.get(`shiFu/quotation/viewOrderQuotation?coachId=${coachId}&pageNum=${pageNum}&pageSize=${pageSize}`)
	},
	updateQuxiaoBao(param) {
		return req.post("shiFu/quotation/cancelOrderQuotation", param)
	},
	//订单详情
	orderdetM(param) {
		return req.get(`shiFu/orderDetails/orderDetails?orderId=${param}`)
	},
	//获取师傅信息
	getMaster(param) {
		return req.get("coach/coachInfo", param)
	},

	// loginuserInfo(param) {
	// 	return req.post("api/v1/wxLogin", param)
	// },
	// userInfo(param) {
	// 	return req.get("user/info", param)
	// },
	//检测师傅信息
	checkMaster(param) {
		return req.post("shiFu/index/shiFuAuth", param)
	},
}