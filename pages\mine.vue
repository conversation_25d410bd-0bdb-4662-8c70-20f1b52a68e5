<template>
	<view class="pages-mine">
		<!-- Header Section -->
		<view class="header">
			<view class="header-content">
				<view class="avatar_view">
					<image mode="aspectFill" class="avatar" :src="userInfo.avatarUrl"></image>
				</view>
				<view class="user-info">
					<view v-if="!isLoggedIn">
						<button @click="showLoginPopup" :disabled="isLoading" :class="{ 'loading': isLoading }">
							{{ isLoading ? '登录中...' : '用户登录' }}
						</button>
					</view>
					<view v-else class="user-info-logged">
						<view class="nickname">{{ userInfo.nickName }}</view>
						<view v-if="userInfo.phone" class="phone-number">{{ userInfo.phone }}</view>
						<view v-else class="bind-phone-container">
							<button @click="showBindPhonePopup" class="bind-phone-btn" :disabled="isBindingPhone">
								{{ isBindingPhone ? '绑定中...' : '绑定手机号' }}
							</button>
						</view>
					</view>
				</view>
				<view @click="navigateTo('../user/userProfile')" class="settings">
					<i class="iconfont icon-xitong text-bold"></i>
				</view>
			</view>
		</view>

		<!-- Action Buttons Section (My Orders) -->
		<view class="mine-menu-list box-shadow fill-base box1">
			<view class="menu-title flex-between pl-lg pr-md b-1px-b">
				<view class="f-paragraph c-title text-bold">我的订单</view>
				
			</view>
			<view @click="dingyue()" class="flex-warp pt-lg pb-lg">
				<view class="order-item" v-for="(item, index) in orderList" :key="index" @tap="navigateTo(item.url)">
					<view class="icon-container">
						<u-icon :name="item.icon" color="#448cfb" size="28"></u-icon>
						<view v-if="item.count > 0" class="number-circle">{{ item.count }}</view>
					</view>
					<view class="mt-sm">{{ item.text }}</view>
				</view>
			</view>
		</view>

		<!-- Spacer -->
		<view class="spacer"></view>

		<!-- Menu List Section -->
		<view class="mine-tool-list fill-base">
			<view class="flex-warp">
				<view class="list-item" :class="{ 'master-side': item.text === '切换师傅版' || item.text === '邀请有礼' }"
					v-for="(item, index) in toolList" :key="index" @tap="navigateTo(item.url)">
					<u-icon :name="item.icon"
						:color="item.text === '切换师傅版' || item.text === '邀请有礼' ? '#E41F19' : '#599eff'" size="24"></u-icon>
					<view class="item-text">{{ item.text }}</view>
				</view>
			</view>
		</view>

		<!-- Floating Contact Button -->
		<view class="floating-contact">
			<view class="contact-container">
				<u-icon name="server-man" color="#576b95" size="24"></u-icon>
				<button class="contact-btn" open-type="contact" bindcontact="handleContact" session-from="sessionFrom">
					客服
				</button>
			</view>
		</view>

		<!-- Login Popup -->
		<view v-if="loginPopupVisible" class="login-popup-overlay" @tap="hideLoginPopup">
			<view class="login-popup" @tap.stop>
				<!-- Close Button -->
				<view class="close-btn" @tap="hideLoginPopup">
					<i class="iconfont icon-close"></i>
				</view>

				<!-- Popup Content -->
				<view class="popup-content">
					<view class="welcome-title">欢迎登录今师傅</view>
					<view class="welcome-subtitle">登录后即可享受完整服务</view>

					<!-- Agreement Checkbox -->
					<view class="agreement-section">
						<view class="checkbox-container" @tap="toggleAgreement">
							<view class="checkbox" :class="{ 'checked': agreedToTerms }">
								<i v-if="agreedToTerms" class="iconfont icon-check">✓</i>
							</view>
							<view class="agreement-text">
								我已阅读并同意 <text class="link" @tap.stop="navigateToAgreement('service')">《今师傅服务协议》</text>
								<text class="link" @tap.stop="navigateToAgreement('privacy')">《隐私政策》</text>
							</view>
						</view>
					</view>

					<!-- Login Button -->
					<button class="phone-login-btn" :class="{ 'disabled': !agreedToTerms || isLoading }"
						:disabled="!agreedToTerms || isLoading" open-type="getPhoneNumber"
						@getphonenumber="onGetPhoneNumber">
						{{ isLoading ? '登录中...' : '手机号快捷登录' }}
					</button>
				</view>
			</view>
		</view>

		<!-- Bind Phone Popup -->
		<view v-if="bindPhonePopupVisible" class="login-popup-overlay" @tap="hideBindPhonePopup">
			<view class="login-popup" @tap.stop>
				<!-- Close Button -->
				<view class="close-btn" @tap="hideBindPhonePopup">
					<i class="iconfont icon-close"></i>
				</view>

				<!-- Popup Content -->
				<view class="popup-content">
					<view class="welcome-title">绑定手机号</view>
					<view class="welcome-subtitle">绑定手机号后可享受完整服务</view>

					<!-- Phone Input -->
					<view class="input-group">
						<view class="input-item">
							<view class="input-icon">
								<u-icon name="phone" color="#3b82f6" size="18"></u-icon>
							</view>
							<input class="input-field" type="number" placeholder="请输入手机号" v-model="bindPhoneForm.phone" maxlength="11" />
						</view>
						<view class="input-item">
							<view class="input-icon">
								<u-icon name="chat" color="#3b82f6" size="18"></u-icon>
							</view>
							<input class="input-field" type="number" placeholder="请输入验证码" v-model="bindPhoneForm.code" maxlength="6" />
							<view class="sms-btn" @click="sendBindPhoneSmsCode" :class="{ disabled: bindPhoneSmsCountdown > 0 }">
								{{ bindPhoneSmsCountdown > 0 ? `${bindPhoneSmsCountdown}s` : '获取验证码' }}
							</view>
						</view>
					</view>

					<!-- Bind Button -->
					<button class="phone-login-btn" :class="{ 'disabled': !canBindPhone || isBindingPhone }"
						:disabled="!canBindPhone || isBindingPhone" @click="handleBindPhone">
						{{ isBindingPhone ? '绑定中...' : '绑定手机号' }}
					</button>
				</view>
			</view>
		</view>

		<!-- Tabbar -->
		<tabbar cur="3"></tabbar>
	</view>
</template>

<script>
	import tabbar from "@/components/tabbar.vue";
	import {
		mapState,
		mapMutations
	} from 'vuex';

	// Utility function for debouncing
	const debounce = (func, wait) => {
		let timeout;
		return function(...args) {
			const context = this;
			clearTimeout(timeout);
			timeout = setTimeout(() => func.apply(context, args), wait);
		};
	};

	export default {
		components: {
			tabbar
		},
		data() {
			return {
				isLoading: false,
				inviteCode: '',
				tmplIds: [
					'qt5Q70oqYNTB2RaTzl0peEHpjK-iinWr3ZfxGvs4wtg',
					'_2z7Bbw8oq4av-yqP31fZLaI82Z_N52wNM_1ihXQv6I',
					'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'
				],
				code: '', // Store wx.login code
				loginPopupVisible: false, // Control login popup visibility
				agreedToTerms: false, // Control agreement checkbox state
				// 绑定手机号相关
				bindPhonePopupVisible: false,
				isBindingPhone: false,
				bindPhoneSmsCountdown: 0,
				bindPhoneSmsTimer: null,
				bindPhoneForm: {
					phone: '',
					code: ''
				},
				toolList: [{
						icon: 'coupon',
						text: '优惠券',
						url: '../user/coupon'
					},
					{
						icon: 'map',
						text: '我的地址',
						url: '../user/address'
					},
					{
						icon: 'level',
						text: '师傅入驻',
						url: '../shifu/Settle'
					},
					// {
					// 	icon: 'man-add',
					// 	text: '切换师傅版',
					// 	url: '../shifu/Receiving'
					// },
					{
						icon: 'red-packet-fill',
						text: '邀请有礼',
						url: '../user/promotion'
					},
					{
						icon: 'man-add',
						text: '代理商申请',
						url: '../user/agent_apply'
					},
					{
						icon: 'man-add',
						text: '提现管理',
						url: '../user/coreWallet'
					}
				],
				activityItem: {
					icon: 'heart-fill',
					text: '限时活动',
					url: '../user/huodong_index'
				},
				orderList: [{
						icon: 'order',
						text: '全部订单',
						url: '../user/order_list?tab=0',
						count: 0
					},
					{
						icon: 'file-text',
						text: '报价列表',
						url: '../user/order_list?tab=-2',
						count: 0
					},
					{
						icon: 'hourglass-half-fill',
						text: '待支付',
						url: '../user/order_list?tab=1',
						count: 0
					},
					{
						icon: 'bell',
						text: '待服务',
						url: '../user/order_list?tab=5',
						count: 0
					},
					{
						icon: 'clock',
						text: '服务中',
						url: '../user/order_list?tab=6',
						count: 0
					},
					{
						icon: 'thumb-up',
						text: '已完成',
						url: '../user/order_list?tab=7',
						count: 0
					}
				]
			};
		},
		computed: {
			...mapState({
				storeUserInfo: state => state.user.userInfo || {},
				token: state => state.user.autograph || '',
				erweima: state => state.user.erweima || ''
			}),
			isLoggedIn() {
				// 如果有token，就认为已登录，即使用户信息还在加载中
				return !!this.token;
			},
			userInfo() {
				// 优先从Vuex获取，如果没有则从本地存储获取
				const vuexUserInfo = this.storeUserInfo;
				const localUserId = uni.getStorageSync('userId');
				const localPhone = uni.getStorageSync('phone');
				const localNickName = uni.getStorageSync('nickName');
				const localAvatarUrl = uni.getStorageSync('avatarUrl');
				const localPid = uni.getStorageSync('pid');

				const result = {
					phone: vuexUserInfo.phone || localPhone || '',
					avatarUrl: vuexUserInfo.avatarUrl || localAvatarUrl || '/static/mine/default_user.png',
					nickName: vuexUserInfo.nickName || localNickName || '微信用户',
					userId: vuexUserInfo.userId || localUserId || '',
					pid: vuexUserInfo.pid || localPid || ''
				};

				// 添加调试信息
				console.log('userInfo computed 被调用');
				console.log('vuexUserInfo:', vuexUserInfo);
				console.log('本地存储数据:', {
					userId: localUserId,
					phone: localPhone,
					nickName: localNickName,
					avatarUrl: localAvatarUrl,
					pid: localPid
				});
				console.log('最终userInfo结果:', result);

				return result;
			},
			canBindPhone() {
				return this.bindPhoneForm.phone && this.bindPhoneForm.code &&
					   this.validatePhone(this.bindPhoneForm.phone);
			}
		},
		onLoad(options) {
			// Get current location
			this.getNowPosition();
			// Handle invite code from options
			if (options.inviteCode) {
				console.log('Received inviteCode:', options.inviteCode);
				this.inviteCode = options.inviteCode;
				uni.setStorageSync('receivedInviteCode', options.inviteCode);
			}
			// Handle erweima from Vuex or storage
			if (this.erweima) {
				console.log('erweima from Vuex:', this.erweima);
				this.inviteCode = this.erweima;
				uni.setStorageSync('receivedInviteCode', this.erweima);
			} else {
				const erweima = uni.getStorageSync('erweima');
				if (erweima) {
					console.log('erweima from storage:', erweima);
					this.$store.commit('setErweima', erweima);
					this.inviteCode = erweima;
					uni.setStorageSync('receivedInviteCode', erweima);
				}
			}
			// Perform WeChat login
			 // #ifdef MP-WEIXIN
			uni.login({
				provider: 'weixin',
				success: res => {
					if (res.code) {
						this.code = res.code;
						console.log('Initial wx.login code:', this.code);
					}
				},
				fail: err => {
					console.error('wx.login failed:', err);
				}
						
			});
			// #endif
			// Fetch activity config
			// this.gethuodongconfig();
			// Initialize user data
			this.initUserData();
			// Fetch highlight if logged in
			if (this.isLoggedIn) {
				this.debounceGetHighlight();
			}
		},
		onShow() {
			console.log('mine页面onShow，检查登录状态');
			console.log('token:', this.token);
			console.log('storeUserInfo:', this.storeUserInfo);

			// 如果有token，初始化用户数据
			if (this.token) {
				console.log('有token，初始化用户数据');
				this.initUserData();

				// 如果Vuex中没有用户信息，尝试获取
				if (!this.storeUserInfo.userId) {
					console.log('Vuex中没有用户信息，尝试获取');
					this.fetchUserInfo();
				}

				this.debounceGetHighlight();
			} else {
				console.log('没有token，处理未登录状态');
				// Ensure UI reflects logged-out state
				this.handleInvalidSession();
			}
		},
		onPullDownRefresh() {
			// Handle pull-down refresh
			if (this.isLoggedIn && this.token) {
				Promise.all([
					this.fetchUserInfo(),
					this.getHighlight()
				]).then(() => {
					uni.stopPullDownRefresh();
					// this.showToast('刷新成功', 'success');
				}).catch(err => {
					console.error('Pull-down refresh failed:', err);
					uni.stopPullDownRefresh();
					// this.showToast('刷新失败，请稍后重试');
				});
			} else {
				// If not logged in, reset UI and stop refresh
				this.handleInvalidSession();
				uni.stopPullDownRefresh();
				this.showToast('请先登录');
			}
		},
		methods: {
			getmylogin() {
				uni.login({
					provider: 'weixin',
					success: res => {
						if (res.code) {
							this.code = res.code;
							console.log('Initial wx.login code:', this.code);
						}
					}
				});
			},
			gethuodongconfig() {
				this.$api.service.huodongselectActivityConfig().then(res => {
					if (res.code === "200") {
						// Add activity item if not already present
						if (!this.toolList.some(item => item.text === this.activityItem.text)) {
							this.toolList = [...this.toolList, this.activityItem];
						}
					} else {
						// Remove activity item if present
						this.toolList = this.toolList.filter(item => item.text !== this.activityItem.text);
					}
					console.log('huodongselectActivityConfig response:', res);
				}).catch(err => {
					console.error('huodongselectActivityConfig failed:', err);
					// Ensure activity item is removed on error
					this.toolList = this.toolList.filter(item => item.text !== this.activityItem.text);
				});
			},
			getNowPosition() {
				return new Promise((resolve) => {
					uni.getLocation({
						type: "gcj02",
						isHighAccuracy: true,
						accuracy: "best",
						success: (res) => {
							uni.setStorageSync("lat", res.latitude);
							uni.setStorageSync("lng", res.longitude);
							uni.request({
								url: `https://restapi.amap.com/v3/geocode/regeo?key=4272f5716dfd17882409f306c0299666&location=${res.longitude},${res.latitude}`,
								success: (res1) => {
									const province = res1.data.regeocode.addressComponent.province;
									this.position = typeof res1.data.regeocode.addressComponent.city === "string" ?
										res1.data.regeocode.addressComponent.city : province;
									uni.setStorageSync("city", {
										city_id: res1.data.regeocode.addressComponent.adcode,
										position: this.position
									});
									resolve();
								},
								fail: (err) => {
									console.error("逆地理编码失败:", err);
									resolve();
								}
							});
						},
						fail: (err) => {
							console.error("获取定位失败:", err);
							resolve();
						}
					});
				});
			},
			getshifuinfo() {
				this.$api.shifu.checkMaster().then(res => {
					if (res.code === "200") {
						uni.redirectTo({
							url: '../shifu/mine'
						});
					}
				});
			},
			// Debounced getHighlight to prevent multiple rapid calls
			debounceGetHighlight: debounce(function() {
				this.getHighlight();
			}, 300),
			getHighlight() {
				const userId = uni.getStorageSync('userId');
				if (!userId) {
					console.log('No userId, skipping getHighlight');
					return Promise.resolve();
				}
				this.isLoading = true;
				return this.$api.service.getHighlight({
					userId: userId,
					role: 1
				}).then(res => {
					console.log('getHighlight response:', res);
					// Create a new array to ensure reactivity
					const updatedOrderList = this.orderList.map((item, index) => ({
						...item,
						count: index === 0 ? (res && res.countOrder ? res.countOrder : 0) :
							index === 1 ? (res && res.shiFuBaoJia ? res.shiFuBaoJia : 0) :
							index === 2 ? (res && res.daiZhiFu ? res.daiZhiFu : 0) :
							index === 3 ? (res && res.daiFuWu ? res.daiFuWu : 0) :
							index === 4 ? (res && res.fuWuZhong ? res.fuWuZhong : 0) :
							index === 5 ? (res && res.yiWanCheng ? res.yiWanCheng : 0) : 0
					}));
					// Update orderList reactively
					this.$set(this, 'orderList', updatedOrderList);
				}).finally(() => {
					this.isLoading = false;
				});
			},
			handleContact(e) {
				console.log(e.detail.path);
				console.log(e.detail.query);
			},
			...mapMutations(['updateUserItem']),
			// 获取当前平台类型
			getCurrentPlatform() {
				// #ifdef APP-PLUS
				return 'app-plus';
				// #endif

				// #ifdef MP-WEIXIN
				return 'mp-weixin';
				// #endif

				// #ifdef H5
				return 'h5';
				// #endif

				return 'unknown';
			},

			showLoginPopup() {
				const platform = this.getCurrentPlatform();
				console.log('当前平台:', platform);

				// 检查当前运行环境
				// #ifdef APP-PLUS
				console.log('APP端跳转到登录页面，isapp: 1');
				uni.navigateTo({
					url: '/pages/login',
					fail: (err) => {
						console.error('跳转登录页面失败:', err);
						this.showToast('跳转失败，请重试');
					}
				});
				// #endif

				// #ifndef APP-PLUS
				console.log('非APP端显示登录弹窗，isapp: 0');
				this.loginPopupVisible = true;
				// #endif

				// 运行时检查作为备用方案
				const systemInfo = uni.getSystemInfoSync();
				console.log('系统信息:', {
					platform: systemInfo.platform,
					app: systemInfo.app,
					uniPlatform: platform
				});
			},
			dingyue() {
				const panduan = uni.getStorageSync('userId');
				console.log(panduan);
				if (panduan) {
					console.log('dingyue called');
					const allTmplIds = this.tmplIds;
					if (allTmplIds.length < 3) {
						console.error("Not enough template IDs available:", allTmplIds);
						return;
					}
					const shuffled = [...allTmplIds].sort(() => 0.5 - Math.random());
					const selectedTmplIds = shuffled.slice(0, 3);
					console.log("Selected template IDs:", selectedTmplIds);
					const templateData = selectedTmplIds.map((id, index) => ({
						templateId: id,
						templateCategoryId: index === 0 ? 10 : 5
					}));
					uni.requestSubscribeMessage({
						tmplIds: selectedTmplIds,
						success: (res) => {
							console.log('requestSubscribeMessage success:', res, 'with tmplIds:', this.tmplIds);
							// Check if any of the template IDs were rejected
							const hasRejection = this.tmplIds.some(tmplId => res[tmplId] === 'reject');
							const hasShownModal = uni.getStorageSync('hasShownSubscriptionModal');
							if (hasRejection && !hasShownModal) {
								uni.showModal({
									title: '提示',
									content: '您已关闭消息订阅，建议点击‘通知管理’开启，方便及时接收师傅的报价通知。',
									cancelText: '取消',
									confirmText: '去开启',
									confirmColor: '#007AFF',
									success: (modalRes) => {
										uni.setStorageSync('hasShownSubscriptionModal', true);
										if (modalRes.confirm) {
											uni.openSetting({
												withSubscriptions: true
											});
										} else if (modalRes.cancel) {
											uni.setStorageSync('hasCanceledSubscription', true);
										}
									}
								});
							}
							this.templateCategoryIds = [];
							selectedTmplIds.forEach((templId, index) => {
								console.log(`Template ${templId} status: ${res[templId]}`);
								if (res[templId] === 'accept') {
									const templateCategoryId = templateData[index].templateCategoryId;
									if (templateCategoryId === 10) {
										for (let i = 0; i < 15; i++) {
											this.templateCategoryIds.push(templateCategoryId);
										}
									} else {
										this.templateCategoryIds.push(templateCategoryId);
									}
									console.log('Accepted message push for template:', templId);
								}
							});
							console.log('Updated templateCategoryIds:', this.templateCategoryIds);
						},
						fail: (err) => {
							console.error('requestSubscribeMessage failed:', err);
						}
					});
				}
			},
			hideLoginPopup() {
				this.loginPopupVisible = false;
				this.agreedToTerms = false;
			},
			toggleAgreement() {
				this.agreedToTerms = !this.agreedToTerms;
			},

			// 绑定手机号相关方法
			showBindPhonePopup() {
				this.bindPhonePopupVisible = true;
			},
			hideBindPhonePopup() {
				this.bindPhonePopupVisible = false;
				this.bindPhoneForm = { phone: '', code: '' };
				if (this.bindPhoneSmsTimer) {
					clearInterval(this.bindPhoneSmsTimer);
					this.bindPhoneSmsTimer = null;
					this.bindPhoneSmsCountdown = 0;
				}
			},

			// 验证手机号
			validatePhone(phone) {
				const phoneReg = /^1[3-9]\d{9}$/;
				return phoneReg.test(phone);
			},

			// 发送绑定手机号验证码
			async sendBindPhoneSmsCode() {
				if (this.bindPhoneSmsCountdown > 0) return;

				const phone = this.bindPhoneForm.phone;
				if (!this.validatePhone(phone)) {
					return this.showToast('请输入正确的手机号');
				}

				try {
					// 调用发送验证码接口
					const response = await this.$api.base.sendSmsCode({ phone });

					if (response.code === '200') {
						this.showToast('验证码发送成功', 'success');
						this.startBindPhoneCountdown();
					} else {
						this.showToast(response.msg || '验证码发送失败，请重试');
					}
				} catch (error) {
					console.error('发送验证码失败:', error);
					this.showToast('验证码发送失败，请重试');
				}
			},

			// 开始绑定手机号倒计时
			startBindPhoneCountdown() {
				this.bindPhoneSmsCountdown = 60;
				this.bindPhoneSmsTimer = setInterval(() => {
					this.bindPhoneSmsCountdown--;
					if (this.bindPhoneSmsCountdown <= 0) {
						clearInterval(this.bindPhoneSmsTimer);
						this.bindPhoneSmsTimer = null;
					}
				}, 1000);
			},
		
			navigateToAgreement(type) {
				let url = '../user/configuser';
				if (type === 'service') {
					url += '?type=service';
				} else if (type === 'privacy') {
					url += '?type=privacy';
				}
				uni.navigateTo({
					url: url
				});
			},

			// 处理绑定手机号
			async handleBindPhone() {
				if (!this.canBindPhone || this.isBindingPhone) return;

				const { phone, code } = this.bindPhoneForm;

				if (!this.validatePhone(phone)) {
					return this.showToast('请输入正确的手机号');
				}

				if (!code) {
					return this.showToast('请输入验证码');
				}

				this.isBindingPhone = true;
				uni.showLoading({ title: '绑定中...' });

				try {
					// 获取unionid
					const unionid = uni.getStorageSync('unionid');
					if (!unionid) {
						throw new Error('缺少微信用户标识，请重新登录');
					}
				const registerID=	uni.getStorageSync("registerID")
					// 调用绑定接口
					const params = {
						phone: phone,
						shortCode: code,
						unionid: unionid,
						platform: 2, // 用户端
						registrationId: registerID // 极光推送id，暂时为空
					};

					console.log('绑定手机号参数:', params);

					const response = await this.$api.user.register(params);
					console.log('绑定手机号响应:', response);

					if (response.code === '200') {
						this.showToast('绑定成功', 'success');

						// 更新用户信息
						const updatedUserInfo = {
							...this.storeUserInfo,
							phone: phone
						};

						this.updateUserItem({
							key: 'userInfo',
							val: updatedUserInfo
						});

						// 更新本地存储
						uni.setStorageSync('phone', phone);

						// 关闭弹窗
						this.hideBindPhonePopup();

						// 刷新用户信息
						this.fetchUserInfo();
					} else {
						throw new Error(response.msg || '绑定失败，请重试');
					}
				} catch (error) {
					console.error('绑定手机号失败:', error);
					this.showToast(error.message || '绑定失败，请重试');
				} finally {
					this.isBindingPhone = false;
					uni.hideLoading();
				}
			},
			initUserData() {
				console.log('initUserData被调用');
				console.log('当前token:', this.token);
				console.log('当前storeUserInfo:', this.storeUserInfo);

				if (this.token && !this.storeUserInfo.userId) {
					console.log('有token但Vuex中没有用户信息，从本地存储恢复');

					const userInfo = {
						phone: uni.getStorageSync('phone') || '',
						avatarUrl: uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',
						nickName: uni.getStorageSync('nickName') || '微信用户',
						userId: uni.getStorageSync('userId') || '',
						pid: uni.getStorageSync('pid') || '',
						unionid: uni.getStorageSync('unionid') || ''
					};

					console.log('从本地存储获取的用户信息:', userInfo);

					// 只要有userId就保存用户信息（微信登录可能没有手机号）
					if (userInfo.userId) {
						console.log('保存用户信息到Vuex');
						this.updateUserItem({
							key: 'userInfo',
							val: userInfo
						});
					} else {
						console.log('没有userId，处理无效会话');
						this.handleInvalidSession();
					}
				} else if (this.token && this.storeUserInfo.userId) {
					console.log('token和用户信息都存在，无需初始化');
				} else {
					console.log('没有token，跳过初始化');
				}
			},
			navigateTo(url) {
				if (!url) return;
				const requiresLogin = [
					// '../user/coupon',
					// '../user/repair_record',
					// '../user/order_list',
					// '../user/address',
					// '../user/Settle',
					// '../user/agent_apply',
					// '../user/promotion',
					// '../user/bankCard',
					// '../shifu/Settle',
					// '../shifu/Receiving',
					// '../shifu/mine'
				];
				if (requiresLogin.some(path => url.startsWith(path)) && !this.isLoggedIn) {
					return this.showToast('请先登录');
				}
				uni.navigateTo({
					url
				});
			},
			fetchUserInfo() {
				if (this.isLoading || !this.token) {
					console.log('Skipping fetchUserInfo: no token or already loading');
					return Promise.resolve();
				}
				this.isLoading = true;
				return this.$api.user.userInfo()
					.then(responses => {
						let response=responses.data
						if (!response || typeof response !== 'object') {
							throw new Error('获取用户信息失败: 响应数据无效');
						}
						const userInfo = {
							phone: response.phone || '',
							avatarUrl: response.avatarUrl || '/static/mine/default_user.png',
							nickName: response.nickName || '微信用户',
							userId: response.id || '',
							createTime: response.createTime || '',
							pid: response.pid || '',
							inviteCode: response.inviteCode || ''
						};
						this.updateUserItem({
							key: 'userInfo',
							val: userInfo
						});
						this.saveUserInfoToStorage(userInfo);
					})
					.catch(error => {
						console.error('获取用户信息失败:', error);
						if (error.message && error.message.includes('响应数据无效')) {
							this.handleInvalidSession();
						} else {
							if (this.token) {
								this.showToast('获取用户信息失败，请稍后重试');
							}
						}
					})
					.finally(() => {
						this.isLoading = false;
					});
			},
			saveUserInfoToStorage(userInfo) {
				uni.setStorageSync('phone', userInfo.phone);
				uni.setStorageSync('avatarUrl', userInfo.avatarUrl);
				uni.setStorageSync('nickName', userInfo.nickName);
				uni.setStorageSync('userId', userInfo.userId);
				uni.setStorageSync('pid', userInfo.pid);
				if (userInfo.unionid) {
					uni.setStorageSync('unionid', userInfo.unionid);
				}
			},
			handleInvalidSession() {
				['token', 'phone', 'avatarUrl', 'nickName', 'userId', 'pid', 'unionid'].forEach(key => {
					uni.removeStorageSync(key);
				});
				this.updateUserItem({
					key: 'userInfo',
					val: {}
				});
				this.updateUserItem({
					key: 'autograph',
					val: ''
				});
				this.isLoading = false;
				// Reset orderList counts to 0 when session is invalid
				this.$set(this, 'orderList', this.orderList.map(item => ({
					...item,
					count: 0
				})));
			},
			onGetPhoneNumber(e) {
				console.log('微信授权登录');
				if (e.detail.errMsg !== 'getPhoneNumber:ok') {
					return this.showToast('授权失败，请重试');
				}
				this.getmylogin();
				this.isLoading = true;
				uni.showLoading({
					mask: true,
					title: '登录中...'
				});
				const {
					encryptedData,
					iv
				} = e.detail;
				uni.checkSession({
					success: () => {
						this.loginWithWeixin({
							code: this.code,
							encryptedData,
							iv,
							pid: this.inviteCode
						});
					},
					fail: () => {
						uni.login({
							provider: 'weixin',
							success: res => {
								if (res.code) {
									this.code = res.code;
									console.log('Refreshed wx.login code:', this.code);
									this.loginWithWeixin({
										code: this.code,
										encryptedData,
										iv,
										pid: this.inviteCode
									});
								} else {
									this.isLoading = false;
									uni.hideLoading();
									this.showToast('获取登录凭证失败');
								}
							},
							fail: () => {
								this.isLoading = false;
								uni.hideLoading();
								this.showToast('微信登录失败，请重试');
							}
						});
					}
				});
			},
			loginWithWeixin(params) {
				// 获取平台类型
				const isapp = this.getCurrentPlatform() === 'app-plus' ? 1 : 0;
				console.log('微信小程序登录平台类型 isapp:', isapp);

				this.$api.user.loginuserInfo({
						code: params.code,
						encryptedData: params.encryptedData,
						iv: params.iv,
						pid: this.inviteCode,
						isapp: isapp // 添加平台类型参数
					})
					.then(response => {
						console.log(response)
						if (!response || !response.data.token) {
							throw new Error('请重新登录');
						}
						uni.setStorageSync('token', response.data.token);
						this.updateUserItem({
							key: 'autograph',
							val: response.data.token
						});
						return this.$api.user.userInfo();
					})
					.then(userInfoRess => {
						let userInfoRes=userInfoRess.data
						if (!userInfoRes || typeof userInfoRes !== 'object') {
							throw new Error('获取用户信息失败');
						}
						const userInfo = {
							phone: userInfoRes.phone || '',
							avatarUrl: userInfoRes.avatarUrl || '/static/mine/default_user.png',
							nickName: userInfoRes.nickName || '微信用户',
							userId: userInfoRes.id || '',
							createTime: userInfoRes.createTime || '',
							pid: userInfoRes.pid || ''
						};
						this.updateUserItem({
							key: 'userInfo',
							val: userInfo
						});
						this.saveUserInfoToStorage(userInfo);
						this.showToast('登录成功', 'success');
						this.hideLoginPopup();
						this.debounceGetHighlight();
						// this.getshifuinfo();
					})
					.catch(error => {
						console.error('Login error:', error);
						this.showToast(error.message || '登录失败，请稍后重试');
						this.handleInvalidSession();
					})
					.finally(() => {
						this.isLoading = false;
						uni.hideLoading();
					});
			},
			showToast(title, icon = 'none') {
				uni.showToast({
					title,
					icon,
					duration: 2000
				});
			}
		}
	};
</script>

<style lang="scss">
	/* Login Popup Styles */
	.login-popup-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 2000;
		display: flex;
		align-items: flex-end;
		justify-content: center;
	}

	.login-popup {
		background-color: #fff;
		width: 100%;
		border-radius: 40rpx 40rpx 0 0;
		position: relative;
		max-height: 60vh;
		padding-bottom: 10rpx;
		animation: slideUp 0.3s ease-out;
	}

	@keyframes slideUp {
		from {
			transform: translateY(100%);
		}

		to {
			transform: translateY(0);
		}
	}

	.close-btn {
		position: absolute;
		top: 30rpx;
		right: 30rpx;
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #999;
		font-size: 40rpx;
		z-index: 10;
	}

	.popup-content {
		padding: 80rpx 60rpx 40rpx;
		text-align: center;
	}

	.welcome-title {
		font-size: 48rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
	}

	.welcome-subtitle {
		font-size: 32rpx;
		color: #666;
		margin-bottom: 80rpx;
	}

	.agreement-section {
		margin-bottom: 60rpx;
		display: flex;
		justify-content: center;
	}

	.checkbox-container {
		display: flex;
		align-items: flex-start;
		text-align: left;
		max-width: 560rpx;
	}

	.checkbox {
		width: 36rpx;
		height: 36rpx;
		border: 2rpx solid #ddd;
		border-radius: 6rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
		margin-top: 4rpx;
		flex-shrink: 0;
		background-color: #fff;
		transition: all 0.2s;

		&.checked {
			background-color: #00C853;
			border-color: #00C853;
			color: #fff;
		}

		.iconfont {
			font-size: 24rpx;
		}
	}

	.agreement-text {
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;

		.link {
			color: #00C853;
		}
	}

	.phone-login-btn {
		width: 100%;
		height: 100rpx;
		background: linear-gradient(135deg, #00C853, #4CAF50);
		border: none;
		border-radius: 50rpx;
		color: #fff;
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 8rpx 20rpx rgba(0, 200, 83, 0.3);
		transition: all 0.2s;

		&:active:not(.disabled) {
			transform: translateY(2rpx);
			box-shadow: 0 4rpx 12rpx rgba(0, 200, 83, 0.3);
		}

		&.disabled {
			background: #ccc;
			box-shadow: none;
			opacity: 0.6;
		}

		&::after {
			border: none;
		}
	}

	.bind-phone-container {
		margin-top: 10rpx;
	}

	.bind-phone-btn {
		background: none;
		border: 2rpx solid rgba(255, 255, 255, 0.8);
		border-radius: 32rpx;
		color: #fff;
		font-size: 28rpx;
		line-height: 1.5;
		padding: 8rpx 24rpx;
		transition: all 0.2s;

		&:active:not([disabled]) {
			background: rgba(255, 255, 255, 0.1);
			transform: scale(0.98);
		}

		&[disabled] {
			opacity: 0.6;
		}

		&::after {
			border: none;
		}
	}

	.input-group {
		margin-bottom: 40rpx;

		.input-item {
			display: flex;
			align-items: center;
			background: #f8fafc;
			border: 2rpx solid #e2e8f0;
			border-radius: 16rpx;
			margin-bottom: 24rpx;
			min-height: 88rpx;
			padding: 0;
			transition: all 0.3s ease;

			&:focus-within {
				border-color: #3b82f6;
				box-shadow: 0 0 0 6rpx rgba(59, 130, 246, 0.08);
			}

			.input-icon {
				width: 50rpx;
				height: 50rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-left: 24rpx;
				background: rgba(59, 130, 246, 0.08);
				border-radius: 50%;
				flex-shrink: 0;
			}

			.input-field {
				flex: 1;
				margin-left: 24rpx;
				margin-right: 16rpx;
				font-size: 32rpx;
				color: #1e293b;
				background: transparent;
				border: none;
				outline: none;

				&::placeholder {
					color: #94a3b8;
				}
			}

			.sms-btn {
				background: linear-gradient(135deg, #3b82f6, #1d4ed8);
				color: #fff;
				padding: 16rpx 24rpx;
				border-radius: 12rpx;
				font-size: 24rpx;
				font-weight: 500;
				margin-right: 16rpx;
				transition: all 0.3s ease;
				box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.2);
				flex-shrink: 0;

				&.disabled {
					background: #cbd5e1;
					color: #64748b;
					box-shadow: none;
				}

				&:not(.disabled):active {
					transform: scale(0.95);
				}
			}
		}
	}

	.alternative-login {
		display: flex;
		align-items: center;
		margin-bottom: 40rpx;

		.divider-line {
			flex: 1;
			height: 1rpx;
			background-color: #eee;
		}

		.divider-text {
			font-size: 26rpx;
			color: #999;
			margin: 0 30rpx;
		}
	}

	.sms-login-btn {
		width: 100%;
		height: 88rpx;
		background-color: #fff;
		border: 2rpx solid #ddd;
		border-radius: 44rpx;
		color: #666;
		font-size: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.2s;

		&:active {
			background-color: #f8f8f8;
			border-color: #00C853;
		}

		&::after {
			border: none;
		}

		.iconfont {
			margin-right: 16rpx;
			font-size: 36rpx;
		}
	}

	/* Floating Contact Button Styles */
	.floating-contact {
		position: fixed;
		bottom: 150rpx;
		right: 30rpx;
		z-index: 1000;
		background-color: #fff;
		border-radius: 50rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
		padding: 10rpx 20rpx;
		display: flex;
		align-items: center;
	}

	.contact-container {
		display: flex;
		align-items: center;
	}

	.contact-btn {
		background: none;
		border: none;
		color: #576b95;
		font-size: 30rpx;
		line-height: 1.5;
		padding: 10rpx 20rpx;
		display: flex;
		align-items: center;
	}

	.contact-btn:active {
		background-color: #ededee;
	}

	/* Existing Styles */
	.pages-mine {
		background-color: #f8f8f8;
		min-height: 100vh;
		padding-bottom: 120rpx;

		.header {
			height: 292rpx;
			background-color: #599EFF;
			position: relative;

			.header-content {
				display: flex;
				align-items: center;
				padding: 40rpx 30rpx 0;

				.avatar_view {
					width: 120rpx;
					height: 120rpx;
					border-radius: 50%;
					overflow: hidden;
					box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

					.avatar {
						width: 100%;
						height: 100%;
						border-radius: 50%;
					}
				}

				.user-info {
					flex: 1;
					margin-left: 20rpx;
					color: #fff;

					.user-info-logged {
						display: flex;
						flex-direction: column;
						gap: 10rpx;
					}

					.nickname {
						font-size: 36rpx;
						font-weight: bold;
					}

					.phone-number {
						font-size: 28rpx;
						opacity: 0.9;
					}

					button {
						background: none;
						border: 2rpx solid rgba(255, 255, 255, 0.5);
						border-radius: 32rpx;
						color: #fff;
						font-size: 32rpx;
						line-height: 1.5;
						padding: 10rpx 30rpx;

						&.loading {
							opacity: 0.7;
						}

						&::after {
							border: none;
						}
					}
				}

				.settings {
					padding: 10rpx;

					.icon-xitong {
						font-size: 40rpx;
						color: #fff;
					}
				}
			}
		}

		.box1 {
			margin-top: -20rpx;
			border-radius: 36rpx 36rpx 0 0;
			position: relative;
			z-index: 10;
		}

		.mine-menu-list {
			background-color: #fff;
			margin: 0 20rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);

			.menu-title {
				height: 90rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0 30rpx 0 40rpx;
				border-bottom: 1px solid #f0f0f0;

				.f-paragraph {
					font-size: 32rpx;
					color: #333;
					font-weight: bold;
				}
			}

			.flex-warp {
				display: flex;
				flex-wrap: wrap;
				padding: 30rpx 0;

				.order-item {
					display: flex;
					flex-direction: column;
					align-items: center;
					width: 25%;
					font-size: 25rpx;
					margin-top: 20rpx;
					color: #666;
					transition: transform 0.2s;

					&:active {
						transform: scale(0.95);
					}

					.icon-container {
						position: relative;
						display: flex;
						align-items: center;
						justify-content: center;
					}

					.number-circle {
						position: absolute;
						top: -10rpx;
						right: -5rpx;
						width: 30rpx;
						height: 30rpx;
						background-color: #ff4d4f;
						color: #fff;
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 20rpx;
						font-weight: bold;
					}
				}

				.mt-sm {
					margin-top: 16rpx;
				}
			}
		}

		.spacer {
			height: 20rpx;
			background-color: transparent;
		}

		.mine-tool-list {
			background-color: #fff;
			margin: 0 20rpx 30rpx;
			border-radius: 12rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
			overflow: hidden;
			padding: 20rpx 10rpx;

			.flex-warp {
				display: flex;
				flex-wrap: wrap;
				justify-content: flex-start;
				/* Changed from space-between to flex-start for left alignment */
			}

			.list-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				width: 25%;
				/* Adjusted width to ensure items align left and fit more naturally */
				padding: 20rpx 0;
				font-size: 28rpx;
				color: #333;
				transition: background-color 0.2s;

				&.master-side {
					.item-text {
						color: #E41F19;
					}
				}

				&:active {
					background-color: #f8f8f8;
				}

				.item-text {
					margin-top: 16rpx;
					text-align: center;
				}
			}
		}

		.flex-between {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
	}
</style>